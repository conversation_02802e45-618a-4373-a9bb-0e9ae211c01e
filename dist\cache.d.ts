export declare class SmartCache<T> {
    private max_size;
    private ttl;
    private cache;
    private lock;
    private hit_count;
    private miss_count;
    constructor(maxSize: number, ttl?: number);
    get(key: string): T | null;
    put(key: string, value: T): void;
    clear(): void;
    private evictLRU;
    private isExpired;
    getStats(): Record<string, any>;
}
//# sourceMappingURL=cache.d.ts.map