/**
 * Configuration Management System
 *
 * Comprehensive configuration management for MySQL MCP server including
 * database connection settings, security policies, and caching parameters.
 * Supports environment variable overrides and provides secure defaults.
 *
 * @fileoverview Configuration interfaces and management classes
 * <AUTHOR> MCP Team
 * @since 1.0.0
 */

import { DefaultConfig, StringConstants } from './constants.js';

/**
 * Database Configuration Interface
 *
 * Defines all database connection and pool configuration parameters.
 * Supports MySQL connection settings with SSL, timeouts, and pooling options.
 *
 * @interface DatabaseConfig
 * @since 1.0.0
 */
export interface DatabaseConfig {
  /** MySQL server hostname or IP address */
  host: string;

  /** MySQL server port number (default: 3306) */
  port: number;

  /** Database username for authentication */
  user: string;

  /** Database password for authentication */
  password: string;

  /** Target database name */
  database: string;

  /** Maximum number of connections in the pool */
  connectionLimit: number;

  /** Minimum number of connections to maintain in the pool */
  minConnections: number;

  /** Connection timeout in seconds */
  connectTimeout: number;

  /** Idle connection timeout in seconds */
  idleTimeout: number;

  /** Whether to enable SSL/TLS encryption */
  sslEnabled: boolean;
}

/**
 * Security Configuration Interface
 *
 * Defines security policies and limits for query execution and access control.
 * Includes rate limiting, query restrictions, and result set limitations.
 *
 * @interface SecurityConfig
 * @since 1.0.0
 */
export interface SecurityConfig {
  /** Maximum allowed length for SQL queries */
  maxQueryLength: number;

  /** List of allowed SQL query types (SELECT, INSERT, etc.) */
  allowedQueryTypes: string[];

  /** Maximum number of rows that can be returned in a single query */
  maxResultRows: number;

  /** Query execution timeout in seconds */
  queryTimeout: number;

  /** Maximum number of requests per rate limit window */
  rateLimitMax: number;

  /** Rate limiting window duration in seconds */
  rateLimitWindow: number;
}

/**
 * Cache Configuration Interface
 *
 * Defines caching parameters for different types of database metadata.
 * Controls cache sizes and time-to-live settings for optimal performance.
 *
 * @interface CacheConfig
 * @since 1.0.0
 */
export interface CacheConfig {
  /** Maximum number of schema entries to cache */
  schemaCacheSize: number;

  /** Maximum number of table existence checks to cache */
  tableExistsCacheSize: number;

  /** Maximum number of index information entries to cache */
  indexCacheSize: number;

  /** Cache time-to-live in seconds */
  cacheTTL: number;
}

/**
 * Configuration Manager Class
 *
 * Central configuration management class that loads and validates all
 * configuration settings from environment variables with secure defaults.
 * Provides type-safe access to database, security, and cache configurations.
 *
 * @class ConfigurationManager
 * @since 1.0.0
 */
export class ConfigurationManager {
  /** Database connection and pool configuration */
  public database: DatabaseConfig;

  /** Security policies and access control configuration */
  public security: SecurityConfig;

  /** Caching system configuration */
  public cache: CacheConfig;

  /**
   * Configuration Manager Constructor
   *
   * Initializes all configuration sections by loading from environment
   * variables with fallback to secure defaults.
   *
   * @constructor
   */
  constructor() {
    this.database = this.loadDatabaseConfig();
    this.security = this.loadSecurityConfig();
    this.cache = this.loadCacheConfig();
  }

  /**
   * Load Database Configuration
   *
   * Loads database connection settings from environment variables with
   * validation and secure defaults. Supports MySQL connection parameters
   * including SSL, timeouts, and connection pooling.
   *
   * @private
   * @returns {DatabaseConfig} Validated database configuration object
   */
  private loadDatabaseConfig(): DatabaseConfig {
    return {
      host: process.env[StringConstants.ENV_MYSQL_HOST] || StringConstants.DEFAULT_HOST,
      port: parseInt(process.env[StringConstants.ENV_MYSQL_PORT] || DefaultConfig.MYSQL_PORT.toString(), 10),
      user: process.env[StringConstants.ENV_MYSQL_USER] || StringConstants.DEFAULT_USER,
      password: process.env[StringConstants.ENV_MYSQL_PASSWORD] || StringConstants.DEFAULT_PASSWORD,
      database: process.env[StringConstants.ENV_MYSQL_DATABASE] || StringConstants.DEFAULT_DATABASE,
      connectionLimit: parseInt(process.env[StringConstants.ENV_CONNECTION_LIMIT] || DefaultConfig.CONNECTION_LIMIT.toString(), 10),
      minConnections: DefaultConfig.MIN_CONNECTIONS,
      connectTimeout: parseInt(process.env[StringConstants.ENV_CONNECT_TIMEOUT] || DefaultConfig.CONNECT_TIMEOUT.toString(), 10),
      idleTimeout: parseInt(process.env[StringConstants.ENV_IDLE_TIMEOUT] || DefaultConfig.IDLE_TIMEOUT.toString(), 10),
      sslEnabled: (process.env[StringConstants.ENV_MYSQL_SSL] || '').toLowerCase() === StringConstants.TRUE_STRING
    };
  }

  /**
   * Load Security Configuration
   *
   * Loads security policies from environment variables including query
   * restrictions, rate limiting, and access control settings.
   *
   * @private
   * @returns {SecurityConfig} Validated security configuration object
   */
  private loadSecurityConfig(): SecurityConfig {
    const allowedTypesStr = process.env[StringConstants.ENV_ALLOWED_QUERY_TYPES] || StringConstants.DEFAULT_ALLOWED_QUERY_TYPES;
    const allowedTypes = allowedTypesStr.split(',').map(t => t.trim().toUpperCase());

    return {
      maxQueryLength: parseInt(process.env[StringConstants.ENV_MAX_QUERY_LENGTH] || DefaultConfig.MAX_QUERY_LENGTH.toString(), 10),
      allowedQueryTypes: allowedTypes,
      maxResultRows: parseInt(process.env[StringConstants.ENV_MAX_RESULT_ROWS] || DefaultConfig.MAX_RESULT_ROWS.toString(), 10),
      queryTimeout: parseInt(process.env[StringConstants.ENV_QUERY_TIMEOUT] || DefaultConfig.QUERY_TIMEOUT.toString(), 10),
      rateLimitMax: parseInt(process.env[StringConstants.ENV_RATE_LIMIT_MAX] || DefaultConfig.RATE_LIMIT_MAX.toString(), 10),
      rateLimitWindow: parseInt(process.env[StringConstants.ENV_RATE_LIMIT_WINDOW] || DefaultConfig.RATE_LIMIT_WINDOW.toString(), 10)
    };
  }

  /**
   * Load Cache Configuration
   *
   * Loads caching system parameters from environment variables including
   * cache sizes and time-to-live settings for optimal performance.
   *
   * @private
   * @returns {CacheConfig} Validated cache configuration object
   */
  private loadCacheConfig(): CacheConfig {
    return {
      schemaCacheSize: parseInt(process.env.SCHEMA_CACHE_SIZE || DefaultConfig.SCHEMA_CACHE_SIZE.toString(), 10),
      tableExistsCacheSize: parseInt(process.env.TABLE_EXISTS_CACHE_SIZE || DefaultConfig.TABLE_EXISTS_CACHE_SIZE.toString(), 10),
      indexCacheSize: parseInt(process.env.INDEX_CACHE_SIZE || DefaultConfig.INDEX_CACHE_SIZE.toString(), 10),
      cacheTTL: parseInt(process.env.CACHE_TTL || DefaultConfig.CACHE_TTL.toString(), 10)
    };
  }

  /**
   * Export Configuration for Diagnostics
   *
   * Returns a sanitized configuration object suitable for diagnostics and
   * logging. Sensitive information like passwords are masked for security.
   *
   * @public
   * @returns {Record<string, any>} Sanitized configuration object
   *
   * @example
   * const config = manager.toObject();
   * console.log(JSON.stringify(config, null, 2));
   */
  public toObject(): Record<string, any> {
    const configObj = {
      database: { ...this.database },
      security: { ...this.security },
      cache: { ...this.cache }
    };

    // Mask sensitive information for security
    configObj.database.password = '***';

    return configObj;
  }

  /**
   * Get Configuration Summary
   *
   * Returns a concise summary of key configuration parameters as strings
   * for quick status checks and monitoring dashboards.
   *
   * @public
   * @returns {Record<string, string>} Key configuration parameters as strings
   *
   * @example
   * const summary = manager.getSummary();
   * console.log(`Database: ${summary.database_host}:${summary.database_port}`);
   */
  public getSummary(): Record<string, string> {
    return {
      database_host: this.database.host,
      database_port: this.database.port.toString(),
      connection_limit: this.database.connectionLimit.toString(),
      max_result_rows: this.security.maxResultRows.toString(),
      rate_limit_max: this.security.rateLimitMax.toString(),
      schema_cache_size: this.cache.schemaCacheSize.toString()
    };
  }
}