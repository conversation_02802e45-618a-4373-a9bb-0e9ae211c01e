/**
 * MySQL Connection Management with Pooling
 *
 * Advanced connection pool management system for MySQL with health monitoring,
 * automatic reconnection, and performance optimization. Provides enterprise-grade
 * connection handling with statistics tracking and graceful shutdown capabilities.
 *
 * @fileoverview MySQL connection pool implementation with health monitoring
 * <AUTHOR> MCP Team
 * @since 1.0.0
 */

import { createPool, Pool, PoolConnection } from 'mysql2/promise';
import { DatabaseConfig } from './config.js';
import { StringConstants, DefaultConfig } from './constants.js';

/**
 * Connection Pool Class
 *
 * Manages MySQL database connections using a connection pool with automatic
 * health monitoring, pre-connection warming, and comprehensive statistics tracking.
 *
 * Features:
 * - Automatic connection pool management
 * - Health check monitoring with configurable intervals
 * - Pre-creation of minimum connections for performance
 * - Connection statistics and performance metrics
 * - Graceful shutdown with proper resource cleanup
 * - SSL/TLS support with configurable security settings
 *
 * @class ConnectionPool
 * @since 1.0.0
 */
export class ConnectionPool {
  /** Database configuration settings */
  private config: DatabaseConfig;

  /** MySQL connection pool instance */
  private pool: Pool | null = null;

  /** Health check monitoring interval timer */
  private healthCheckInterval: NodeJS.Timeout | null = null;

  /** Shutdown event flag for graceful termination */
  private shutdownEvent: boolean = false;

  /** Connection pool performance statistics */
  private connectionStats: Record<string, number> = {
    pool_hits: 0,
    pool_waits: 0
  };

  /**
   * Connection Pool Constructor
   *
   * Initializes the connection pool with the provided database configuration.
   * The pool is created lazily on first connection request.
   *
   * @constructor
   * @param {DatabaseConfig} config - Database connection configuration
   */
  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  /**
   * Initialize Connection Pool
   *
   * Creates and configures the MySQL connection pool with security settings,
   * pre-warms connections, and starts health monitoring. This method is
   * idempotent and can be called multiple times safely.
   *
   * @public
   * @returns {Promise<void>} Promise that resolves when pool is initialized
   * @throws {Error} When pool creation or initialization fails
   *
   * @example
   * await connectionPool.initialize();
   */
  public async initialize(): Promise<void> {
    // Skip initialization if pool already exists
    if (this.pool) {
      return;
    }

    try {
      // Configure connection pool with security and performance settings
      const poolConfig: any = {
        host: this.config.host,
        port: this.config.port,
        user: this.config.user,
        password: this.config.password,
        database: this.config.database,
        connectionLimit: this.config.connectionLimit,
        acquireTimeout: this.config.connectTimeout * 1000,
        timeout: this.config.connectTimeout * 1000,
        charset: StringConstants.CHARSET,
        multipleStatements: false, // Security: disable multiple statements to prevent SQL injection
        ssl: this.config.sslEnabled ? {} : false
      };

      // Create the connection pool
      this.pool = createPool(poolConfig);

      // Pre-create minimum connections for better initial performance
      await this.preCreateConnections();

      // Start periodic health monitoring
      this.startHealthCheck();

    } catch (error) {
      throw new Error(`${StringConstants.MSG_FAILED_TO_INIT_POOL} ${error}`);
    }
  }

  /**
   * Pre-create Minimum Connections
   *
   * Creates the minimum number of connections specified in configuration
   * to improve initial performance by avoiding connection creation delays
   * on first requests.
   *
   * @private
   * @returns {Promise<void>} Promise that resolves when connections are pre-created
   */
  private async preCreateConnections(): Promise<void> {
    if (!this.pool) return;

    try {
      // Create promises for minimum connections
      const promises: Promise<PoolConnection>[] = [];
      for (let i = 0; i < this.config.minConnections; i++) {
        promises.push(this.pool.getConnection());
      }

      // Wait for all connections to be created
      const connections = await Promise.all(promises);

      // Release connections back to pool for reuse
      connections.forEach(conn => conn.release());
    } catch (error) {
      // Pre-creation failure is not critical for overall functionality
      console.warn('Warning: Failed to pre-create connections:', error);
    }
  }

  /**
   * Start Health Check Monitoring
   *
   * Initiates periodic health checks to ensure connection pool remains
   * healthy and responsive. Clears any existing health check interval
   * before starting a new one.
   *
   * @private
   */
  private startHealthCheck(): void {
    // Clear existing health check interval if present
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Start new health check interval
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, DefaultConfig.HEALTH_CHECK_INTERVAL * 1000);
  }

  /**
   * Perform Health Check
   *
   * Executes a simple query to verify that the connection pool is
   * functioning correctly. Failures are logged but don't affect
   * normal operation.
   *
   * @private
   * @returns {Promise<void>} Promise that resolves when health check completes
   */
  private async performHealthCheck(): Promise<void> {
    if (!this.pool) return;

    try {
      // Get connection and execute simple test query
      const connection = await this.pool.getConnection();
      await connection.execute('SELECT 1');
      connection.release();
    } catch (error) {
      // Health check failure is logged but not critical
      console.warn('Health check failed:', error);
    }
  }

  /**
   * Get Database Connection
   *
   * Retrieves a connection from the pool with automatic initialization
   * and performance tracking. Measures connection acquisition time and
   * updates statistics for monitoring.
   *
   * @public
   * @returns {Promise<PoolConnection>} Promise resolving to a database connection
   * @throws {Error} When pool initialization fails or connection cannot be acquired
   *
   * @example
   * const connection = await pool.getConnection();
   * try {
   *   const [rows] = await connection.execute('SELECT * FROM users');
   *   return rows;
   * } finally {
   *   connection.release();
   * }
   */
  public async getConnection(): Promise<PoolConnection> {
    // Initialize pool if not already done
    if (!this.pool) {
      await this.initialize();
    }

    // Verify pool was successfully initialized
    if (!this.pool) {
      throw new Error('Connection pool not initialized');
    }

    // Track connection acquisition time for performance monitoring
    const startTime = Date.now();
    const connection = await this.pool.getConnection();
    const waitTime = Date.now() - startTime;

    // Update connection statistics based on wait time
    if (waitTime > 100) { // More than 100ms indicates pool pressure
      this.connectionStats[StringConstants.FIELD_POOL_WAITS]++;
    } else {
      this.connectionStats[StringConstants.FIELD_POOL_HITS]++;
    }

    return connection;
  }

  /**
   * Get Pool Statistics
   *
   * Returns comprehensive statistics about the connection pool including
   * configuration, performance metrics, and health status for monitoring
   * and debugging purposes.
   *
   * @public
   * @returns {Record<string, any>} Pool statistics and configuration information
   *
   * @example
   * const stats = pool.getStats();
   * console.log(`Pool hits: ${stats.connection_stats.pool_hits}`);
   */
  public getStats(): Record<string, any> {
    if (!this.pool) {
      return { [StringConstants.STATUS_KEY]: StringConstants.STATUS_NOT_INITIALIZED };
    }

    // Return available pool statistics and configuration
    // Note: In a production implementation, we would access more pool internals
    return {
      [StringConstants.FIELD_POOL_NAME]: StringConstants.POOL_NAME,
      [StringConstants.FIELD_POOL_SIZE]: this.config.connectionLimit,
      [StringConstants.FIELD_CONNECTION_STATS]: { ...this.connectionStats },
      [StringConstants.FIELD_HEALTH_CHECK_ACTIVE]: !!this.healthCheckInterval
    };
  }

  /**
   * Close Connection Pool
   *
   * Performs graceful shutdown of the connection pool including stopping
   * health checks, closing all connections, and cleaning up resources.
   * Should be called during application shutdown.
   *
   * @public
   * @returns {Promise<void>} Promise that resolves when pool is fully closed
   *
   * @example
   * // Graceful shutdown
   * await pool.close();
   */
  public async close(): Promise<void> {
    // Set shutdown flag to prevent new operations
    this.shutdownEvent = true;

    // Stop health check monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Close connection pool and release all connections
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
    }
  }
}