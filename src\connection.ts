// MySQL Connection Management with Pooling
import { createPool, Pool, PoolConnection, QueryOptions } from 'mysql2/promise';
import { DatabaseConfig } from './config.js';
import { StringConstants, DefaultConfig } from './constants.js';

export class ConnectionPool {
  private config: DatabaseConfig;
  private pool: Pool | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private shutdownEvent: boolean = false;
  private connectionStats: Record<string, number> = {
    pool_hits: 0,
    pool_waits: 0
  };

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  // Initialize connection pool
  public async initialize(): Promise<void> {
    if (this.pool) {
      return;
    }

    try {
      const poolConfig: any = {
        host: this.config.host,
        port: this.config.port,
        user: this.config.user,
        password: this.config.password,
        database: this.config.database,
        connectionLimit: this.config.connectionLimit,
        acquireTimeout: this.config.connectTimeout * 1000,
        timeout: this.config.connectTimeout * 1000,
        charset: StringConstants.CHARSET,
        multipleStatements: false, // Security: disable multiple statements
        ssl: this.config.sslEnabled ? {} : false
      };

      this.pool = createPool(poolConfig);
      
      // Pre-create minimum connections
      await this.preCreateConnections();
      
      // Start health check
      this.startHealthCheck();
      
    } catch (error) {
      throw new Error(`${StringConstants.MSG_FAILED_TO_INIT_POOL} ${error}`);
    }
  }

  // Pre-create minimum connections
  private async preCreateConnections(): Promise<void> {
    if (!this.pool) return;
    
    try {
      const promises: Promise<PoolConnection>[] = [];
      for (let i = 0; i < this.config.minConnections; i++) {
        promises.push(this.pool.getConnection());
      }
      
      const connections = await Promise.all(promises);
      
      // Release connections back to pool
      connections.forEach(conn => conn.release());
    } catch (error) {
      // Pre-creation failure doesn't affect overall initialization
      console.warn('Warning: Failed to pre-create connections:', error);
    }
  }

  // Start health check
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, DefaultConfig.HEALTH_CHECK_INTERVAL * 1000);
  }

  // Perform health check
  private async performHealthCheck(): Promise<void> {
    if (!this.pool) return;
    
    try {
      const connection = await this.pool.getConnection();
      await connection.execute('SELECT 1');
      connection.release();
    } catch (error) {
      // Health check failure is not critical
      console.warn('Health check failed:', error);
    }
  }

  // Get database connection
  public async getConnection(): Promise<PoolConnection> {
    if (!this.pool) {
      await this.initialize();
    }
    
    if (!this.pool) {
      throw new Error('Connection pool not initialized');
    }
    
    const startTime = Date.now();
    const connection = await this.pool.getConnection();
    const waitTime = Date.now() - startTime;
    
    // Update connection statistics
    if (waitTime > 100) { // More than 100ms wait
      this.connectionStats[StringConstants.FIELD_POOL_WAITS]++;
    } else {
      this.connectionStats[StringConstants.FIELD_POOL_HITS]++;
    }
    
    return connection;
  }

  // Get pool statistics
  public getStats(): Record<string, any> {
    if (!this.pool) {
      return { [StringConstants.STATUS_KEY]: StringConstants.STATUS_NOT_INITIALIZED };
    }
    
    // Note: In a real implementation, we would access pool internals
    // For now, we'll return what we can safely access
    return {
      [StringConstants.FIELD_POOL_NAME]: StringConstants.POOL_NAME,
      [StringConstants.FIELD_POOL_SIZE]: this.config.connectionLimit,
      [StringConstants.FIELD_CONNECTION_STATS]: { ...this.connectionStats },
      [StringConstants.FIELD_HEALTH_CHECK_ACTIVE]: !!this.healthCheckInterval
    };
  }

  // Close connection pool
  public async close(): Promise<void> {
    this.shutdownEvent = true;
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
    }
  }
}