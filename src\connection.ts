/**
 * MySQL 连接管理与连接池
 *
 * 高级的 MySQL 连接池管理系统，具有健康监控、自动重连和性能优化功能。
 * 提供企业级的连接处理，包括统计跟踪和优雅关闭功能。
 *
 * @fileoverview 带健康监控的 MySQL 连接池实现
 * <AUTHOR> MCP 团队
 * @since 1.0.0
 */

import { createPool, Pool, PoolConnection } from 'mysql2/promise';
import { DatabaseConfig } from './config.js';
import { StringConstants, DefaultConfig } from './constants.js';

/**
 * 连接池类
 *
 * 使用连接池管理 MySQL 数据库连接，具有自动健康监控、
 * 预连接预热和全面的统计跟踪功能。
 *
 * 功能特性：
 * - 自动连接池管理
 * - 可配置间隔的健康检查监控
 * - 为性能预创建最小连接数
 * - 连接统计和性能指标
 * - 带适当资源清理的优雅关闭
 * - 可配置安全设置的 SSL/TLS 支持
 *
 * @class ConnectionPool
 * @since 1.0.0
 */
export class ConnectionPool {
  /** 数据库配置设置 */
  private config: DatabaseConfig;

  /** MySQL 连接池实例 */
  private pool: Pool | null = null;

  /** 健康检查监控间隔计时器 */
  private healthCheckInterval: NodeJS.Timeout | null = null;

  /** 优雅终止的关闭事件标志 */
  private shutdownEvent: boolean = false;

  /** 连接池性能统计 */
  private connectionStats: Record<string, number> = {
    pool_hits: 0,
    pool_waits: 0
  };

  /**
   * Connection Pool Constructor
   *
   * Initializes the connection pool with the provided database configuration.
   * The pool is created lazily on first connection request.
   *
   * @constructor
   * @param {DatabaseConfig} config - Database connection configuration
   */
  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  /**
   * Initialize Connection Pool
   *
   * Creates and configures the MySQL connection pool with security settings,
   * pre-warms connections, and starts health monitoring. This method is
   * idempotent and can be called multiple times safely.
   *
   * @public
   * @returns {Promise<void>} Promise that resolves when pool is initialized
   * @throws {Error} When pool creation or initialization fails
   *
   * @example
   * await connectionPool.initialize();
   */
  public async initialize(): Promise<void> {
    // Skip initialization if pool already exists
    if (this.pool) {
      return;
    }

    try {
      // Configure connection pool with security and performance settings
      const poolConfig: any = {
        host: this.config.host,
        port: this.config.port,
        user: this.config.user,
        password: this.config.password,
        database: this.config.database,
        connectionLimit: this.config.connectionLimit,
        acquireTimeout: this.config.connectTimeout * 1000,
        timeout: this.config.connectTimeout * 1000,
        charset: StringConstants.CHARSET,
        multipleStatements: false, // Security: disable multiple statements to prevent SQL injection
        ssl: this.config.sslEnabled ? {} : false
      };

      // Create the connection pool
      this.pool = createPool(poolConfig);

      // Pre-create minimum connections for better initial performance
      await this.preCreateConnections();

      // Start periodic health monitoring
      this.startHealthCheck();

    } catch (error) {
      throw new Error(`${StringConstants.MSG_FAILED_TO_INIT_POOL} ${error}`);
    }
  }

  /**
   * Pre-create Minimum Connections
   *
   * Creates the minimum number of connections specified in configuration
   * to improve initial performance by avoiding connection creation delays
   * on first requests.
   *
   * @private
   * @returns {Promise<void>} Promise that resolves when connections are pre-created
   */
  private async preCreateConnections(): Promise<void> {
    if (!this.pool) return;

    try {
      // Create promises for minimum connections
      const promises: Promise<PoolConnection>[] = [];
      for (let i = 0; i < this.config.minConnections; i++) {
        promises.push(this.pool.getConnection());
      }

      // Wait for all connections to be created
      const connections = await Promise.all(promises);

      // Release connections back to pool for reuse
      connections.forEach(conn => conn.release());
    } catch (error) {
      // Pre-creation failure is not critical for overall functionality
      console.warn('Warning: Failed to pre-create connections:', error);
    }
  }

  /**
   * Start Health Check Monitoring
   *
   * Initiates periodic health checks to ensure connection pool remains
   * healthy and responsive. Clears any existing health check interval
   * before starting a new one.
   *
   * @private
   */
  private startHealthCheck(): void {
    // Clear existing health check interval if present
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Start new health check interval
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, DefaultConfig.HEALTH_CHECK_INTERVAL * 1000);
  }

  /**
   * Perform Health Check
   *
   * Executes a simple query to verify that the connection pool is
   * functioning correctly. Failures are logged but don't affect
   * normal operation.
   *
   * @private
   * @returns {Promise<void>} Promise that resolves when health check completes
   */
  private async performHealthCheck(): Promise<void> {
    if (!this.pool) return;

    try {
      // Get connection and execute simple test query
      const connection = await this.pool.getConnection();
      await connection.execute('SELECT 1');
      connection.release();
    } catch (error) {
      // Health check failure is logged but not critical
      console.warn('Health check failed:', error);
    }
  }

  /**
   * Get Database Connection
   *
   * Retrieves a connection from the pool with automatic initialization
   * and performance tracking. Measures connection acquisition time and
   * updates statistics for monitoring.
   *
   * @public
   * @returns {Promise<PoolConnection>} Promise resolving to a database connection
   * @throws {Error} When pool initialization fails or connection cannot be acquired
   *
   * @example
   * const connection = await pool.getConnection();
   * try {
   *   const [rows] = await connection.execute('SELECT * FROM users');
   *   return rows;
   * } finally {
   *   connection.release();
   * }
   */
  public async getConnection(): Promise<PoolConnection> {
    // Initialize pool if not already done
    if (!this.pool) {
      await this.initialize();
    }

    // Verify pool was successfully initialized
    if (!this.pool) {
      throw new Error('Connection pool not initialized');
    }

    // Track connection acquisition time for performance monitoring
    const startTime = Date.now();
    const connection = await this.pool.getConnection();
    const waitTime = Date.now() - startTime;

    // Update connection statistics based on wait time
    if (waitTime > 100) { // More than 100ms indicates pool pressure
      this.connectionStats[StringConstants.FIELD_POOL_WAITS]++;
    } else {
      this.connectionStats[StringConstants.FIELD_POOL_HITS]++;
    }

    return connection;
  }

  /**
   * Get Pool Statistics
   *
   * Returns comprehensive statistics about the connection pool including
   * configuration, performance metrics, and health status for monitoring
   * and debugging purposes.
   *
   * @public
   * @returns {Record<string, any>} Pool statistics and configuration information
   *
   * @example
   * const stats = pool.getStats();
   * console.log(`Pool hits: ${stats.connection_stats.pool_hits}`);
   */
  public getStats(): Record<string, any> {
    if (!this.pool) {
      return { [StringConstants.STATUS_KEY]: StringConstants.STATUS_NOT_INITIALIZED };
    }

    // Return available pool statistics and configuration
    // Note: In a production implementation, we would access more pool internals
    return {
      [StringConstants.FIELD_POOL_NAME]: StringConstants.POOL_NAME,
      [StringConstants.FIELD_POOL_SIZE]: this.config.connectionLimit,
      [StringConstants.FIELD_CONNECTION_STATS]: { ...this.connectionStats },
      [StringConstants.FIELD_HEALTH_CHECK_ACTIVE]: !!this.healthCheckInterval
    };
  }

  /**
   * Close Connection Pool
   *
   * Performs graceful shutdown of the connection pool including stopping
   * health checks, closing all connections, and cleaning up resources.
   * Should be called during application shutdown.
   *
   * @public
   * @returns {Promise<void>} Promise that resolves when pool is fully closed
   *
   * @example
   * // Graceful shutdown
   * await pool.close();
   */
  public async close(): Promise<void> {
    // Set shutdown flag to prevent new operations
    this.shutdownEvent = true;

    // Stop health check monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Close connection pool and release all connections
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
    }
  }
}