/**
 * MySQL MCP 服务器常量
 *
 * 包含 MySQL 错误代码、默认配置、字符串常量和系统参数的综合常量集合。
 * 提供集中的配置管理和错误处理。
 *
 * @fileoverview 中央常量和配置定义
 * <AUTHOR> MCP 团队
 * @since 1.0.0
 */

/**
 * MySQL 错误代码
 *
 * 按类别组织的标准 MySQL 错误代码，用于正确的错误处理和用户友好的错误消息。
 * 基于官方 MySQL 文档。
 *
 * 类别：
 * - 访问控制：身份验证和授权错误
 * - 对象解析：数据库对象未找到错误
 * - 数据完整性：约束违反和重复键错误
 * - SQL 语法：查询解析和语法错误
 * - 连接：网络和连接相关错误
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // 检查特定错误类型
 * if (error.code === MySQLErrorCodes.ACCESS_DENIED) {
 *   throw new Error('无效凭据');
 * }
 */
export const MySQLErrorCodes = {
  /** 访问控制错误 - 身份验证和授权失败 */
  ACCESS_DENIED: 1045,                    // 一般访问拒绝
  ACCESS_DENIED_FOR_USER: 1044,           // 用户特定访问拒绝
  TABLE_ACCESS_DENIED: 1142,              // 表级访问拒绝
  COLUMN_ACCESS_DENIED: 1143,             // 列级访问拒绝

  /** 对象解析错误 - 数据库对象未找到 */
  UNKNOWN_DATABASE: 1049,                 // 数据库不存在
  TABLE_DOESNT_EXIST: 1146,               // 表不存在
  UNKNOWN_COLUMN: 1054,                   // 列不存在
  UNKNOWN_TABLE: 1109,                    // 表引用错误

  /** 数据完整性错误 - 约束违反和重复 */
  DUPLICATE_ENTRY: 1062,                  // 重复键值
  DUPLICATE_ENTRY_WITH_KEY_NAME: 1586,    // 带键名的重复键
  DUPLICATE_KEY_NAME: 1557,               // 重复键名

  /** SQL 语法错误 - 查询解析和语法问题 */
  PARSE_ERROR: 1064,                      // SQL 语法错误
  SYNTAX_ERROR: 1149,                     // 一般语法错误
  PARSE_ERROR_NEAR: 1065,                 // 特定标记附近的语法错误

  /** 连接错误 - 网络和连接问题 */
  CANT_CONNECT_TO_SERVER: 2003,           // 无法连接到 MySQL 服务器
  LOST_CONNECTION: 2013,                  // 查询期间连接丢失
  SERVER_HAS_GONE_AWAY: 2006,             // MySQL 服务器已断开
} as const;

/**
 * 默认配置
 *
 * 所有系统组件的综合默认配置值。提供安全、性能优化的默认值，
 * 可通过环境变量覆盖。
 *
 * 配置类别：
 * - MySQL 连接：数据库连接和连接池设置
 * - 安全：访问控制和验证限制
 * - 缓存：缓存大小和过期设置
 * - 性能：监控和优化参数
 * - 可靠性：重试和错误处理配置
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // 使用环境变量覆盖的默认值
 * const port = process.env.MYSQL_PORT || DefaultConfig.MYSQL_PORT;
 */
export const DefaultConfig = {
  /** MySQL 连接配置 - 数据库连接设置 */
  MYSQL_PORT: 3306,                       // 标准 MySQL 端口
  CONNECTION_LIMIT: 10,                    // 连接池中的最大连接数
  MIN_CONNECTIONS: 2,                      // 要维护的最小连接数
  CONNECT_TIMEOUT: 60,                     // 连接超时（秒）
  IDLE_TIMEOUT: 60,                        // 空闲连接超时（秒）

  /** 安全配置 - 访问控制和验证限制 */
  MAX_QUERY_LENGTH: 10000,                 // 最大 SQL 查询长度
  MAX_RESULT_ROWS: 1000,                   // 每个查询结果的最大行数
  QUERY_TIMEOUT: 30,                       // 查询执行超时（秒）
  RATE_LIMIT_WINDOW: 60,                   // 速率限制窗口（秒）
  RATE_LIMIT_MAX: 100,                     // 每个窗口的最大请求数

  /** 输入验证配置 - 数据验证限制 */
  MAX_INPUT_LENGTH: 1000,                  // 最大输入字符串长度
  MAX_TABLE_NAME_LENGTH: 64,               // 最大表名长度

  /** 缓存配置 - 缓存系统参数 */
  SCHEMA_CACHE_SIZE: 128,                  // 模式缓存条目限制
  TABLE_EXISTS_CACHE_SIZE: 64,             // 表存在性缓存限制
  INDEX_CACHE_SIZE: 64,                    // 索引信息缓存限制
  CACHE_TTL: 300,                          // 缓存过期时间（秒）

  /** 连接池配置 - 连接池管理设置 */
  HEALTH_CHECK_INTERVAL: 30,               // 健康检查频率（秒）
  CONNECTION_MAX_AGE: 3600,                // 最大连接年龄（秒）

  /** 性能监控配置 - 指标和监控 */
  METRICS_WINDOW_SIZE: 1000,               // 要保留的指标数据点
  SLOW_QUERY_THRESHOLD: 1.0,               // 慢查询阈值（秒）

  /** 重试配置 - 错误处理和恢复 */
  RECONNECT_ATTEMPTS: 3,                   // 数据库重连尝试次数
  RECONNECT_DELAY: 1,                      // 重连尝试之间的延迟
  MAX_RETRY_ATTEMPTS: 3,                   // 最大操作重试次数

  /** 批处理配置 - 批量操作设置 */
  BATCH_SIZE: 1000,                        // 默认批处理大小

  /** 日志配置 - 日志输出和格式化 */
  MAX_LOG_DETAIL_LENGTH: 100,              // 最大日志详细信息长度
} as const;

/**
 * 字符串常量
 *
 * 用于一致消息传递、配置键和系统标识符的集中字符串常量。
 * 按功能类别组织，便于维护和本地化支持。
 *
 * 类别：
 * - 数据库配置：连接和连接池设置
 * - 环境变量：配置覆盖键
 * - SQL 操作：查询类型和 SQL 相关常量
 * - 错误消息：用户友好的错误消息模板
 * - 系统状态：状态代码和状态指示器
 * - 字段名称：JSON 响应字段标识符
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // 使用字符串常量保持一致性
 * const host = process.env[StringConstants.ENV_MYSQL_HOST] || StringConstants.DEFAULT_HOST;
 */
export const StringConstants = {
  /** 数据库配置字符串 - 默认连接设置 */
  DEFAULT_HOST: "localhost",               // 默认 MySQL 主机
  DEFAULT_USER: "root",                    // 默认 MySQL 用户名
  DEFAULT_PASSWORD: "",                    // 默认 MySQL 密码（空）
  DEFAULT_DATABASE: "",                    // 默认数据库名称（空）
  POOL_NAME: "mysql_mcp_pool",             // 连接池标识符
  CHARSET: "utf8mb4",                      // 默认字符集
  SQL_MODE: "TRADITIONAL",                 // 默认 SQL 模式

  // Environment variable keys
  ENV_MYSQL_HOST: "MYSQL_HOST",
  ENV_MYSQL_PORT: "MYSQL_PORT",
  ENV_MYSQL_USER: "MYSQL_USER",
  ENV_MYSQL_PASSWORD: "MYSQL_PASSWORD",
  ENV_MYSQL_DATABASE: "MYSQL_DATABASE",
  ENV_MYSQL_SSL: "MYSQL_SSL",
  ENV_CONNECTION_LIMIT: "MYSQL_CONNECTION_LIMIT",
  ENV_CONNECT_TIMEOUT: "MYSQL_CONNECT_TIMEOUT",
  ENV_IDLE_TIMEOUT: "MYSQL_IDLE_TIMEOUT",
  ENV_ALLOWED_QUERY_TYPES: "ALLOWED_QUERY_TYPES",
  ENV_MAX_QUERY_LENGTH: "MAX_QUERY_LENGTH",
  ENV_MAX_RESULT_ROWS: "MAX_RESULT_ROWS",
  ENV_QUERY_TIMEOUT: "QUERY_TIMEOUT",
  ENV_RATE_LIMIT_WINDOW: "RATE_LIMIT_WINDOW",
  ENV_RATE_LIMIT_MAX: "RATE_LIMIT_MAX",

  // SQL query types
  SQL_SELECT: "SELECT",
  SQL_SHOW: "SHOW",
  SQL_DESCRIBE: "DESCRIBE",
  SQL_INSERT: "INSERT",
  SQL_UPDATE: "UPDATE",
  SQL_DELETE: "DELETE",
  SQL_CREATE: "CREATE",
  SQL_DROP: "DROP",
  SQL_ALTER: "ALTER",

  // Default allowed query types
  DEFAULT_ALLOWED_QUERY_TYPES: "SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER",

  // Dangerous SQL patterns
  DANGEROUS_PATTERNS: ["--", "/*", "*/", "xp_", "sp_"],

  // Error categories
  ERROR_CATEGORY_ACCESS_DENIED: "access_denied",
  ERROR_CATEGORY_OBJECT_NOT_FOUND: "object_not_found",
  ERROR_CATEGORY_CONSTRAINT_VIOLATION: "constraint_violation",
  ERROR_CATEGORY_SYNTAX_ERROR: "syntax_error",
  ERROR_CATEGORY_CONNECTION_ERROR: "connection_error",
  ERROR_CATEGORY_UNKNOWN: "unknown",

  // Error severity levels
  SEVERITY_HIGH: "high",
  SEVERITY_MEDIUM: "medium",
  SEVERITY_LOW: "low",

  // Log event types
  LOG_EVENT_SECURITY: "[SECURITY]",

  // Error message templates
  MSG_DATABASE_ACCESS_DENIED: "Database access denied, please check username and password",
  MSG_DATABASE_OBJECT_NOT_FOUND: "Database object not found",
  MSG_DATABASE_CONSTRAINT_VIOLATION: "Data constraint violation",
  MSG_DATABASE_SYNTAX_ERROR: "SQL syntax error",
  MSG_DATABASE_CONNECTION_ERROR: "Database connection error",
  MSG_MYSQL_CONNECTION_POOL_FAILED: "MySQL connection pool creation failed",
  MSG_MYSQL_CONNECTION_ERROR: "MySQL connection error",
  MSG_MYSQL_QUERY_ERROR: "MySQL query error",
  MSG_RATE_LIMIT_EXCEEDED: "Rate limit exceeded. Please try again later.",
  MSG_QUERY_TOO_LONG: "Query exceeds maximum allowed length",
  MSG_PROHIBITED_OPERATIONS: "Query contains prohibited operations",
  MSG_QUERY_TYPE_NOT_ALLOWED: "Query type '{query_type}' is not allowed",
  MSG_INVALID_TABLE_NAME: "Invalid table name format",
  MSG_TABLE_NAME_TOO_LONG: "Table name exceeds maximum length",
  MSG_INVALID_CHARACTER: "Invalid character in {field_name}",
  MSG_INPUT_TOO_LONG: "{field_name} exceeds maximum length",
  MSG_DANGEROUS_CONTENT: "Potentially dangerous content in {field_name}",

  // Status strings
  STATUS_SUCCESS: "success",
  STATUS_FAILED: "failed",
  STATUS_NOT_INITIALIZED: "not_initialized",
  STATUS_ERROR: "error",
  STATUS_KEY: "status",
  ERROR_KEY: "error",

  // Special values
  NULL_BYTE: '\x00',
  TRUE_STRING: "true",

  // Server related constants
  SERVER_NAME: "mysql-mcp-server",

  // Error messages
  MSG_FASTMCP_NOT_INSTALLED: "Error: FastMCP not installed. Please install with: npm install fastmcp",
  MSG_ERROR_DURING_CLEANUP: "Error during cleanup:",
  MSG_SIGNAL_RECEIVED: "Signal received",
  MSG_GRACEFUL_SHUTDOWN: "Gracefully shutting down...",
  MSG_SERVER_RUNNING: "MySQL MCP Server (FastMCP) running on stdio",
  MSG_SERVER_ERROR: "Server error:",

  // Tool function error messages
  MSG_QUERY_FAILED: "Query failed:",
  MSG_SHOW_TABLES_FAILED: "Show tables failed:",
  MSG_DESCRIBE_TABLE_FAILED: "Describe table failed:",
  MSG_GET_METRICS_FAILED: "Get performance metrics failed:",
  MSG_SELECT_DATA_FAILED: "Select data failed:",
  MSG_INSERT_DATA_FAILED: "Insert data failed:",
  MSG_UPDATE_DATA_FAILED: "Update data failed:",
  MSG_DELETE_DATA_FAILED: "Delete data failed:",
  MSG_GET_SCHEMA_FAILED: "Get schema failed:",
  MSG_GET_INDEXES_FAILED: "Get indexes failed:",
  MSG_GET_FOREIGN_KEYS_FAILED: "Get foreign keys failed:",
  MSG_CREATE_TABLE_FAILED: "Create table failed:",
  MSG_DROP_TABLE_FAILED: "Drop table failed:",
  MSG_DIAGNOSE_FAILED: "Diagnose failed:",
  MSG_GET_POOL_STATUS_FAILED: "Get connection pool status failed:",

  // Connection pool related constants
  MSG_FAILED_TO_INIT_POOL: "Failed to initialize connection pool:",

  // JSON response field constants
  SUCCESS_KEY: "success",

  // SQL keyword constants
  SQL_IF_EXISTS: "IF EXISTS",

  // Performance metrics field constants
  FIELD_QUERY_COUNT: "query_count",
  FIELD_AVG_QUERY_TIME: "avg_query_time",
  FIELD_SLOW_QUERY_COUNT: "slow_query_count",
  FIELD_ERROR_COUNT: "error_count",
  FIELD_ERROR_RATE: "error_rate",
  FIELD_CACHE_HIT_RATE: "cache_hit_rate",
  FIELD_CONNECTION_POOL_HITS: "connection_pool_hits",
  FIELD_CONNECTION_POOL_WAITS: "connection_pool_waits",

  // Cache statistics field constants
  FIELD_SIZE: "size",
  FIELD_MAX_SIZE: "max_size",
  FIELD_HIT_COUNT: "hit_count",
  FIELD_MISS_COUNT: "miss_count",
  FIELD_HIT_RATE: "hit_rate",
  FIELD_TTL: "ttl",

  // Connection pool statistics field constants
  FIELD_POOL_NAME: "pool_name",
  FIELD_POOL_SIZE: "pool_size",
  FIELD_AVAILABLE_CONNECTIONS: "available_connections",
  FIELD_CONNECTION_STATS: "connection_stats",
  FIELD_HEALTH_CHECK_ACTIVE: "health_check_active",
  FIELD_POOL_HITS: "pool_hits",
  FIELD_POOL_WAITS: "pool_waits",

  // Performance report section constants
  SECTION_PERFORMANCE: "performance",
  SECTION_CACHE_STATS: "cache_stats",
  SECTION_CONNECTION_POOL: "connection_pool",
  SECTION_SCHEMA_CACHE: "schema_cache",
  SECTION_TABLE_EXISTS_CACHE: "table_exists_cache",
  SECTION_INDEX_CACHE: "index_cache",

  // Configuration field constants
  FIELD_HOST: "host",
  FIELD_PORT: "port",
  FIELD_DATABASE: "database",
  FIELD_CONNECTION_LIMIT: "connection_limit",
  FIELD_CONNECT_TIMEOUT: "connect_timeout",
  FIELD_SSL_ENABLED: "ssl_enabled",

  // Diagnostic report field constants
  FIELD_CONNECTION_POOL_STATUS: "connection_pool_status",
  FIELD_CONFIG: "config",
  FIELD_SECURITY_CONFIG: "security_config",
  FIELD_PERFORMANCE_METRICS: "performance_metrics",
  FIELD_CONNECTION_TEST: "connection_test",
  FIELD_MAX_QUERY_LENGTH: "max_query_length",
  FIELD_MAX_RESULT_ROWS: "max_result_rows",
  FIELD_RATE_LIMIT_MAX: "rate_limit_max",
  FIELD_ALLOWED_QUERY_TYPES: "allowed_query_types",
  FIELD_RESULT: "result",
} as const;