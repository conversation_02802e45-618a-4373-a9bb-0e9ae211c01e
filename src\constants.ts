/**
 * MySQL MCP Server Constants
 *
 * Comprehensive collection of constants including MySQL error codes,
 * default configurations, string constants, and system parameters.
 * Provides centralized configuration management and error handling.
 *
 * @fileoverview Central constants and configuration definitions
 * <AUTHOR> MCP Team
 * @since 1.0.0
 */

/**
 * MySQL Error Codes
 *
 * Standard MySQL error codes organized by category for proper error handling
 * and user-friendly error messages. Based on official MySQL documentation.
 *
 * Categories:
 * - Access Control: Authentication and authorization errors
 * - Object Resolution: Database object not found errors
 * - Data Integrity: Constraint violation and duplicate key errors
 * - SQL Syntax: Query parsing and syntax errors
 * - Connection: Network and connection-related errors
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // Check for specific error type
 * if (error.code === MySQLErrorCodes.ACCESS_DENIED) {
 *   throw new Error('Invalid credentials');
 * }
 */
export const MySQLErrorCodes = {
  /** Access Control Errors - Authentication and authorization failures */
  ACCESS_DENIED: 1045,                    // General access denied
  ACCESS_DENIED_FOR_USER: 1044,           // User-specific access denied
  TABLE_ACCESS_DENIED: 1142,              // Table-level access denied
  COLUMN_ACCESS_DENIED: 1143,             // Column-level access denied

  /** Object Resolution Errors - Database objects not found */
  UNKNOWN_DATABASE: 1049,                 // Database does not exist
  TABLE_DOESNT_EXIST: 1146,               // Table does not exist
  UNKNOWN_COLUMN: 1054,                   // Column does not exist
  UNKNOWN_TABLE: 1109,                    // Table reference error

  /** Data Integrity Errors - Constraint violations and duplicates */
  DUPLICATE_ENTRY: 1062,                  // Duplicate key value
  DUPLICATE_ENTRY_WITH_KEY_NAME: 1586,    // Duplicate key with key name
  DUPLICATE_KEY_NAME: 1557,               // Duplicate key name

  /** SQL Syntax Errors - Query parsing and syntax issues */
  PARSE_ERROR: 1064,                      // SQL syntax error
  SYNTAX_ERROR: 1149,                     // General syntax error
  PARSE_ERROR_NEAR: 1065,                 // Syntax error near specific token

  /** Connection Errors - Network and connection issues */
  CANT_CONNECT_TO_SERVER: 2003,           // Cannot connect to MySQL server
  LOST_CONNECTION: 2013,                  // Lost connection during query
  SERVER_HAS_GONE_AWAY: 2006,             // MySQL server has gone away
} as const;

/**
 * Default Configuration
 *
 * Comprehensive default configuration values for all system components.
 * Provides secure, performance-optimized defaults that can be overridden
 * via environment variables.
 *
 * Configuration Categories:
 * - MySQL Connection: Database connection and pooling settings
 * - Security: Access control and validation limits
 * - Caching: Cache sizes and expiration settings
 * - Performance: Monitoring and optimization parameters
 * - Reliability: Retry and error handling configuration
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // Use default values with environment override
 * const port = process.env.MYSQL_PORT || DefaultConfig.MYSQL_PORT;
 */
export const DefaultConfig = {
  /** MySQL Connection Configuration - Database connectivity settings */
  MYSQL_PORT: 3306,                       // Standard MySQL port
  CONNECTION_LIMIT: 10,                    // Maximum connections in pool
  MIN_CONNECTIONS: 2,                      // Minimum connections to maintain
  CONNECT_TIMEOUT: 60,                     // Connection timeout (seconds)
  IDLE_TIMEOUT: 60,                        // Idle connection timeout (seconds)

  /** Security Configuration - Access control and validation limits */
  MAX_QUERY_LENGTH: 10000,                 // Maximum SQL query length
  MAX_RESULT_ROWS: 1000,                   // Maximum rows per query result
  QUERY_TIMEOUT: 30,                       // Query execution timeout (seconds)
  RATE_LIMIT_WINDOW: 60,                   // Rate limiting window (seconds)
  RATE_LIMIT_MAX: 100,                     // Maximum requests per window

  /** Input Validation Configuration - Data validation limits */
  MAX_INPUT_LENGTH: 1000,                  // Maximum input string length
  MAX_TABLE_NAME_LENGTH: 64,               // Maximum table name length

  /** Cache Configuration - Caching system parameters */
  SCHEMA_CACHE_SIZE: 128,                  // Schema cache entry limit
  TABLE_EXISTS_CACHE_SIZE: 64,             // Table existence cache limit
  INDEX_CACHE_SIZE: 64,                    // Index information cache limit
  CACHE_TTL: 300,                          // Cache expiration time (seconds)

  /** Connection Pool Configuration - Pool management settings */
  HEALTH_CHECK_INTERVAL: 30,               // Health check frequency (seconds)
  CONNECTION_MAX_AGE: 3600,                // Maximum connection age (seconds)

  /** Performance Monitoring Configuration - Metrics and monitoring */
  METRICS_WINDOW_SIZE: 1000,               // Metrics data points to retain
  SLOW_QUERY_THRESHOLD: 1.0,               // Slow query threshold (seconds)

  /** Retry Configuration - Error handling and recovery */
  RECONNECT_ATTEMPTS: 3,                   // Database reconnection attempts
  RECONNECT_DELAY: 1,                      // Delay between reconnect attempts
  MAX_RETRY_ATTEMPTS: 3,                   // Maximum operation retry attempts

  /** Batch Processing Configuration - Bulk operation settings */
  BATCH_SIZE: 1000,                        // Default batch processing size

  /** Logging Configuration - Log output and formatting */
  MAX_LOG_DETAIL_LENGTH: 100,              // Maximum log detail length
} as const;

/**
 * String Constants
 *
 * Centralized string constants for consistent messaging, configuration keys,
 * and system identifiers. Organized by functional category for easy maintenance
 * and localization support.
 *
 * Categories:
 * - Database Configuration: Connection and pool settings
 * - Environment Variables: Configuration override keys
 * - SQL Operations: Query types and SQL-related constants
 * - Error Messages: User-friendly error message templates
 * - System Status: Status codes and state indicators
 * - Field Names: JSON response field identifiers
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // Use string constants for consistency
 * const host = process.env[StringConstants.ENV_MYSQL_HOST] || StringConstants.DEFAULT_HOST;
 */
export const StringConstants = {
  /** Database Configuration Strings - Default connection settings */
  DEFAULT_HOST: "localhost",               // Default MySQL host
  DEFAULT_USER: "root",                    // Default MySQL username
  DEFAULT_PASSWORD: "",                    // Default MySQL password (empty)
  DEFAULT_DATABASE: "",                    // Default database name (empty)
  POOL_NAME: "mysql_mcp_pool",             // Connection pool identifier
  CHARSET: "utf8mb4",                      // Default character set
  SQL_MODE: "TRADITIONAL",                 // Default SQL mode

  // Environment variable keys
  ENV_MYSQL_HOST: "MYSQL_HOST",
  ENV_MYSQL_PORT: "MYSQL_PORT",
  ENV_MYSQL_USER: "MYSQL_USER",
  ENV_MYSQL_PASSWORD: "MYSQL_PASSWORD",
  ENV_MYSQL_DATABASE: "MYSQL_DATABASE",
  ENV_MYSQL_SSL: "MYSQL_SSL",
  ENV_CONNECTION_LIMIT: "MYSQL_CONNECTION_LIMIT",
  ENV_CONNECT_TIMEOUT: "MYSQL_CONNECT_TIMEOUT",
  ENV_IDLE_TIMEOUT: "MYSQL_IDLE_TIMEOUT",
  ENV_ALLOWED_QUERY_TYPES: "ALLOWED_QUERY_TYPES",
  ENV_MAX_QUERY_LENGTH: "MAX_QUERY_LENGTH",
  ENV_MAX_RESULT_ROWS: "MAX_RESULT_ROWS",
  ENV_QUERY_TIMEOUT: "QUERY_TIMEOUT",
  ENV_RATE_LIMIT_WINDOW: "RATE_LIMIT_WINDOW",
  ENV_RATE_LIMIT_MAX: "RATE_LIMIT_MAX",

  // SQL query types
  SQL_SELECT: "SELECT",
  SQL_SHOW: "SHOW",
  SQL_DESCRIBE: "DESCRIBE",
  SQL_INSERT: "INSERT",
  SQL_UPDATE: "UPDATE",
  SQL_DELETE: "DELETE",
  SQL_CREATE: "CREATE",
  SQL_DROP: "DROP",
  SQL_ALTER: "ALTER",

  // Default allowed query types
  DEFAULT_ALLOWED_QUERY_TYPES: "SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER",

  // Dangerous SQL patterns
  DANGEROUS_PATTERNS: ["--", "/*", "*/", "xp_", "sp_"],

  // Error categories
  ERROR_CATEGORY_ACCESS_DENIED: "access_denied",
  ERROR_CATEGORY_OBJECT_NOT_FOUND: "object_not_found",
  ERROR_CATEGORY_CONSTRAINT_VIOLATION: "constraint_violation",
  ERROR_CATEGORY_SYNTAX_ERROR: "syntax_error",
  ERROR_CATEGORY_CONNECTION_ERROR: "connection_error",
  ERROR_CATEGORY_UNKNOWN: "unknown",

  // Error severity levels
  SEVERITY_HIGH: "high",
  SEVERITY_MEDIUM: "medium",
  SEVERITY_LOW: "low",

  // Log event types
  LOG_EVENT_SECURITY: "[SECURITY]",

  // Error message templates
  MSG_DATABASE_ACCESS_DENIED: "Database access denied, please check username and password",
  MSG_DATABASE_OBJECT_NOT_FOUND: "Database object not found",
  MSG_DATABASE_CONSTRAINT_VIOLATION: "Data constraint violation",
  MSG_DATABASE_SYNTAX_ERROR: "SQL syntax error",
  MSG_DATABASE_CONNECTION_ERROR: "Database connection error",
  MSG_MYSQL_CONNECTION_POOL_FAILED: "MySQL connection pool creation failed",
  MSG_MYSQL_CONNECTION_ERROR: "MySQL connection error",
  MSG_MYSQL_QUERY_ERROR: "MySQL query error",
  MSG_RATE_LIMIT_EXCEEDED: "Rate limit exceeded. Please try again later.",
  MSG_QUERY_TOO_LONG: "Query exceeds maximum allowed length",
  MSG_PROHIBITED_OPERATIONS: "Query contains prohibited operations",
  MSG_QUERY_TYPE_NOT_ALLOWED: "Query type '{query_type}' is not allowed",
  MSG_INVALID_TABLE_NAME: "Invalid table name format",
  MSG_TABLE_NAME_TOO_LONG: "Table name exceeds maximum length",
  MSG_INVALID_CHARACTER: "Invalid character in {field_name}",
  MSG_INPUT_TOO_LONG: "{field_name} exceeds maximum length",
  MSG_DANGEROUS_CONTENT: "Potentially dangerous content in {field_name}",

  // Status strings
  STATUS_SUCCESS: "success",
  STATUS_FAILED: "failed",
  STATUS_NOT_INITIALIZED: "not_initialized",
  STATUS_ERROR: "error",
  STATUS_KEY: "status",
  ERROR_KEY: "error",

  // Special values
  NULL_BYTE: '\x00',
  TRUE_STRING: "true",

  // Server related constants
  SERVER_NAME: "mysql-mcp-server",

  // Error messages
  MSG_FASTMCP_NOT_INSTALLED: "Error: FastMCP not installed. Please install with: npm install fastmcp",
  MSG_ERROR_DURING_CLEANUP: "Error during cleanup:",
  MSG_SIGNAL_RECEIVED: "Signal received",
  MSG_GRACEFUL_SHUTDOWN: "Gracefully shutting down...",
  MSG_SERVER_RUNNING: "MySQL MCP Server (FastMCP) running on stdio",
  MSG_SERVER_ERROR: "Server error:",

  // Tool function error messages
  MSG_QUERY_FAILED: "Query failed:",
  MSG_SHOW_TABLES_FAILED: "Show tables failed:",
  MSG_DESCRIBE_TABLE_FAILED: "Describe table failed:",
  MSG_GET_METRICS_FAILED: "Get performance metrics failed:",
  MSG_SELECT_DATA_FAILED: "Select data failed:",
  MSG_INSERT_DATA_FAILED: "Insert data failed:",
  MSG_UPDATE_DATA_FAILED: "Update data failed:",
  MSG_DELETE_DATA_FAILED: "Delete data failed:",
  MSG_GET_SCHEMA_FAILED: "Get schema failed:",
  MSG_GET_INDEXES_FAILED: "Get indexes failed:",
  MSG_GET_FOREIGN_KEYS_FAILED: "Get foreign keys failed:",
  MSG_CREATE_TABLE_FAILED: "Create table failed:",
  MSG_DROP_TABLE_FAILED: "Drop table failed:",
  MSG_DIAGNOSE_FAILED: "Diagnose failed:",
  MSG_GET_POOL_STATUS_FAILED: "Get connection pool status failed:",

  // Connection pool related constants
  MSG_FAILED_TO_INIT_POOL: "Failed to initialize connection pool:",

  // JSON response field constants
  SUCCESS_KEY: "success",

  // SQL keyword constants
  SQL_IF_EXISTS: "IF EXISTS",

  // Performance metrics field constants
  FIELD_QUERY_COUNT: "query_count",
  FIELD_AVG_QUERY_TIME: "avg_query_time",
  FIELD_SLOW_QUERY_COUNT: "slow_query_count",
  FIELD_ERROR_COUNT: "error_count",
  FIELD_ERROR_RATE: "error_rate",
  FIELD_CACHE_HIT_RATE: "cache_hit_rate",
  FIELD_CONNECTION_POOL_HITS: "connection_pool_hits",
  FIELD_CONNECTION_POOL_WAITS: "connection_pool_waits",

  // Cache statistics field constants
  FIELD_SIZE: "size",
  FIELD_MAX_SIZE: "max_size",
  FIELD_HIT_COUNT: "hit_count",
  FIELD_MISS_COUNT: "miss_count",
  FIELD_HIT_RATE: "hit_rate",
  FIELD_TTL: "ttl",

  // Connection pool statistics field constants
  FIELD_POOL_NAME: "pool_name",
  FIELD_POOL_SIZE: "pool_size",
  FIELD_AVAILABLE_CONNECTIONS: "available_connections",
  FIELD_CONNECTION_STATS: "connection_stats",
  FIELD_HEALTH_CHECK_ACTIVE: "health_check_active",
  FIELD_POOL_HITS: "pool_hits",
  FIELD_POOL_WAITS: "pool_waits",

  // Performance report section constants
  SECTION_PERFORMANCE: "performance",
  SECTION_CACHE_STATS: "cache_stats",
  SECTION_CONNECTION_POOL: "connection_pool",
  SECTION_SCHEMA_CACHE: "schema_cache",
  SECTION_TABLE_EXISTS_CACHE: "table_exists_cache",
  SECTION_INDEX_CACHE: "index_cache",

  // Configuration field constants
  FIELD_HOST: "host",
  FIELD_PORT: "port",
  FIELD_DATABASE: "database",
  FIELD_CONNECTION_LIMIT: "connection_limit",
  FIELD_CONNECT_TIMEOUT: "connect_timeout",
  FIELD_SSL_ENABLED: "ssl_enabled",

  // Diagnostic report field constants
  FIELD_CONNECTION_POOL_STATUS: "connection_pool_status",
  FIELD_CONFIG: "config",
  FIELD_SECURITY_CONFIG: "security_config",
  FIELD_PERFORMANCE_METRICS: "performance_metrics",
  FIELD_CONNECTION_TEST: "connection_test",
  FIELD_MAX_QUERY_LENGTH: "max_query_length",
  FIELD_MAX_RESULT_ROWS: "max_result_rows",
  FIELD_RATE_LIMIT_MAX: "rate_limit_max",
  FIELD_ALLOWED_QUERY_TYPES: "allowed_query_types",
  FIELD_RESULT: "result",
} as const;