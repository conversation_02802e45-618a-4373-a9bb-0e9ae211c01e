/**
 * Rate Limiting Implementation
 *
 * Advanced rate limiting system using token bucket and adaptive algorithms
 * to prevent abuse and ensure fair resource allocation. Supports both
 * fixed-rate and adaptive rate limiting based on system load.
 *
 * @fileoverview Token bucket and adaptive rate limiting implementations
 * <AUTHOR> MCP Team
 * @since 1.0.0
 */

/**
 * Token Bucket Rate Limiter
 *
 * Implements the token bucket algorithm for rate limiting. This algorithm
 * allows for burst traffic while maintaining an average rate limit over time.
 *
 * Algorithm Details:
 * - Tokens are added to the bucket at a constant rate (refill rate)
 * - Each request consumes one or more tokens
 * - Requests are allowed only if sufficient tokens are available
 * - Bucket has a maximum capacity to limit burst size
 *
 * Advantages:
 * - Allows burst traffic up to bucket capacity
 * - Smooth rate limiting without strict timing windows
 * - Memory efficient with O(1) space complexity
 * - Self-regulating based on actual usage patterns
 *
 * @class TokenBucketRateLimiter
 * @since 1.0.0
 */
export class TokenBucketRateLimiter {
  /** Maximum number of tokens the bucket can hold */
  private capacity: number;

  /** Current number of tokens in the bucket */
  private tokens: number;

  /** Rate at which tokens are added (tokens per second) */
  private refillRate: number;

  /** Time window for rate calculation (seconds) */
  private window: number;

  /** Timestamp of last token refill operation */
  private lastRefill: number;

  /**
   * Token Bucket Constructor
   *
   * Initializes the token bucket with specified capacity and refill rate.
   * The bucket starts full to allow immediate requests.
   *
   * @constructor
   * @param {number} capacity - Maximum tokens the bucket can hold
   * @param {number} refillRate - Tokens added per second
   * @param {number} [window=60] - Time window in seconds (for compatibility)
   *
   * @example
   * // Allow 100 requests with refill of 10 per second
   * const limiter = new TokenBucketRateLimiter(100, 10);
   */
  constructor(capacity: number, refillRate: number, window: number = 60) {
    this.capacity = capacity;
    this.tokens = capacity; // Start with full bucket
    this.refillRate = refillRate;
    this.window = window;
    this.lastRefill = Date.now();
  }

  /**
   * Check if Request is Allowed
   *
   * Determines whether a request can be processed based on token availability.
   * Automatically refills tokens based on elapsed time and consumes tokens
   * for allowed requests.
   *
   * Algorithm Steps:
   * 1. Calculate elapsed time since last refill
   * 2. Add tokens based on refill rate and elapsed time
   * 3. Check if sufficient tokens are available
   * 4. Consume tokens if request is allowed
   *
   * Time Complexity: O(1)
   * Space Complexity: O(1)
   *
   * @public
   * @param {number} [tokensRequested=1] - Number of tokens to consume
   * @returns {boolean} True if request is allowed, false if rate limited
   *
   * @example
   * if (limiter.allowRequest()) {
   *   // Process request
   *   processRequest();
   * } else {
   *   // Rate limited
   *   throw new Error('Rate limit exceeded');
   * }
   */
  public allowRequest(tokensRequested: number = 1): boolean {
    const now = Date.now();

    // Calculate tokens to add based on elapsed time
    const elapsed = (now - this.lastRefill) / 1000; // Convert to seconds
    const tokensToAdd = elapsed * this.refillRate;

    // Refill bucket up to capacity
    this.tokens = Math.min(this.capacity, this.tokens + tokensToAdd);
    this.lastRefill = now;

    // Check if sufficient tokens are available
    if (this.tokens >= tokensRequested) {
      this.tokens -= tokensRequested;
      return true;
    }

    return false;
  }
}

/**
 * Adaptive Rate Limiter
 *
 * Advanced rate limiting system that automatically adjusts limits based on
 * system load and resource utilization. Maintains separate token buckets
 * for different identifiers while adapting to system conditions.
 *
 * Features:
 * - Dynamic rate adjustment based on CPU and memory usage
 * - Per-identifier rate limiting with isolated buckets
 * - Automatic bucket creation and management
 * - Load-aware scaling for optimal performance
 *
 * Use Cases:
 * - API rate limiting with system load awareness
 * - Database connection throttling
 * - Resource-aware request processing
 * - Multi-tenant rate limiting
 *
 * @class AdaptiveRateLimiter
 * @since 1.0.0
 */
export class AdaptiveRateLimiter {
  /** Base rate limit before system load adjustments */
  private baseLimit: number;

  /** Time window for rate calculations (seconds) */
  private window: number;

  /** Current system load factor (0.5 to 1.2) */
  private systemLoadFactor: number;

  /** Map of identifier-specific token bucket limiters */
  private buckets: Map<string, TokenBucketRateLimiter>;

  /**
   * Adaptive Rate Limiter Constructor
   *
   * Initializes the adaptive rate limiter with base limits and time window.
   * System load factor starts at 1.0 (no adjustment).
   *
   * @constructor
   * @param {number} baseLimit - Base rate limit before load adjustments
   * @param {number} [window=60] - Time window in seconds
   *
   * @example
   * // Create adaptive limiter with 100 requests per minute
   * const limiter = new AdaptiveRateLimiter(100, 60);
   */
  constructor(baseLimit: number, window: number = 60) {
    this.baseLimit = baseLimit;
    this.window = window;
    this.systemLoadFactor = 1.0; // Start with no adjustment
    this.buckets = new Map<string, TokenBucketRateLimiter>();
  }

  /**
   * Update System Load
   *
   * Adjusts the system load factor based on CPU and memory utilization.
   * This affects all rate limits by scaling the base limit up or down.
   *
   * Load Factor Rules:
   * - High load (CPU > 80% OR Memory > 80%): Factor = 0.5 (reduce limits)
   * - Low load (CPU < 50% AND Memory < 50%): Factor = 1.2 (increase limits)
   * - Normal load: Factor = 1.0 (no adjustment)
   *
   * @public
   * @param {number} cpuUsage - CPU utilization (0.0 to 1.0)
   * @param {number} memoryUsage - Memory utilization (0.0 to 1.0)
   *
   * @example
   * // Update based on system metrics
   * limiter.updateSystemLoad(0.75, 0.60); // Normal load
   * limiter.updateSystemLoad(0.90, 0.85); // High load - reduces limits
   */
  public updateSystemLoad(cpuUsage: number, memoryUsage: number): void {
    if (cpuUsage > 0.8 || memoryUsage > 0.8) {
      // High system load: reduce rate limits to protect system
      this.systemLoadFactor = 0.5;
    } else if (cpuUsage < 0.5 && memoryUsage < 0.5) {
      // Low system load: increase rate limits for better throughput
      this.systemLoadFactor = 1.2;
    } else {
      // Normal system load: use base rate limits
      this.systemLoadFactor = 1.0;
    }
  }

  /**
   * Check Rate Limit for Identifier
   *
   * Checks if a request from the specified identifier should be allowed
   * based on current rate limits and system load. Creates new token buckets
   * for new identifiers automatically.
   *
   * @public
   * @param {string} identifier - Unique identifier for rate limiting
   * @returns {boolean} True if request is allowed, false if rate limited
   *
   * @example
   * // Check rate limit for specific user
   * if (limiter.checkRateLimit('user:123')) {
   *   // Process request
   *   handleRequest();
   * } else {
   *   // Rate limited
   *   throw new Error('Rate limit exceeded for user');
   * }
   */
  public checkRateLimit(identifier: string): boolean {
    // Calculate adjusted limit based on current system load
    const adjustedLimit = Math.floor(this.baseLimit * this.systemLoadFactor);

    // Create new token bucket for new identifiers
    if (!this.buckets.has(identifier)) {
      const refillRate = adjustedLimit / this.window;
      this.buckets.set(identifier, new TokenBucketRateLimiter(
        adjustedLimit,
        refillRate,
        this.window
      ));
    }

    // Check rate limit using the identifier's token bucket
    return this.buckets.get(identifier)!.allowRequest();
  }
}