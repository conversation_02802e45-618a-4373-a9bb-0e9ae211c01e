// Rate Limiting Implementation

// Token bucket rate limiter
export class TokenBucketRateLimiter {
  private capacity: number;
  private tokens: number;
  private refillRate: number; // tokens per second
  private window: number;
  private lastRefill: number;

  constructor(capacity: number, refillRate: number, window: number = 60) {
    this.capacity = capacity;
    this.tokens = capacity;
    this.refillRate = refillRate;
    this.window = window;
    this.lastRefill = Date.now();
  }

  // Check if request is allowed
  public allowRequest(tokensRequested: number = 1): boolean {
    const now = Date.now();
    
    // Refill tokens based on elapsed time
    const elapsed = (now - this.lastRefill) / 1000; // convert to seconds
    const tokensToAdd = elapsed * this.refillRate;
    this.tokens = Math.min(this.capacity, this.tokens + tokensToAdd);
    this.lastRefill = now;

    if (this.tokens >= tokensRequested) {
      this.tokens -= tokensRequested;
      return true;
    }

    return false;
  }
}

// Adaptive rate limiter
export class AdaptiveRateLimiter {
  private baseLimit: number;
  private window: number;
  private systemLoadFactor: number;
  private buckets: Map<string, TokenBucketRateLimiter>;

  constructor(baseLimit: number, window: number = 60) {
    this.baseLimit = baseLimit;
    this.window = window;
    this.systemLoadFactor = 1.0;
    this.buckets = new Map<string, TokenBucketRateLimiter>();
  }

  // Update system load based on resource usage
  public updateSystemLoad(cpuUsage: number, memoryUsage: number): void {
    if (cpuUsage > 0.8 || memoryUsage > 0.8) {
      this.systemLoadFactor = 0.5; // Reduce rate limit
    } else if (cpuUsage < 0.5 && memoryUsage < 0.5) {
      this.systemLoadFactor = 1.2; // Increase rate limit
    } else {
      this.systemLoadFactor = 1.0;
    }
  }

  // Check rate limit for identifier
  public checkRateLimit(identifier: string): boolean {
    const adjustedLimit = Math.floor(this.baseLimit * this.systemLoadFactor);
    
    if (!this.buckets.has(identifier)) {
      const refillRate = adjustedLimit / this.window;
      this.buckets.set(identifier, new TokenBucketRateLimiter(
        adjustedLimit,
        refillRate,
        this.window
      ));
    }

    return this.buckets.get(identifier)!.allowRequest();
  }
}