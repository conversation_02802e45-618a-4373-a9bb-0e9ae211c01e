// Smart Cache Implementation
import { DefaultConfig, StringConstants } from './constants.js';

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  createdAt: number;
  accessCount: number;
  lastAccessed: number;
}

// Smart cache class
export class SmartCache<T> {
  private max_size: number;
  private ttl: number;
  private cache: Map<string, CacheEntry<T>>;
  private lock: boolean;
  private hit_count: number;
  private miss_count: number;

  constructor(maxSize: number, ttl: number = DefaultConfig.CACHE_TTL) {
    this.max_size = maxSize;
    this.ttl = ttl;
    this.cache = new Map<string, CacheEntry<T>>();
    this.lock = false;
    this.hit_count = 0;
    this.miss_count = 0;
  }

  // Get value from cache
  public get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.miss_count++;
      return null;
    }

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.miss_count++;
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.hit_count++;

    // Move to end (most recently used)
    this.cache.delete(key);
    this.cache.set(key, entry);

    return entry.data;
  }

  // Put value in cache
  public put(key: string, value: T): void {
    // If key already exists, update and move to end
    if (this.cache.has(key)) {
      const entry = this.cache.get(key)!;
      entry.data = value;
      entry.createdAt = Date.now();
      entry.accessCount = 0;
      entry.lastAccessed = Date.now();
      this.cache.delete(key);
      this.cache.set(key, entry);
      return;
    }

    // If cache is full, evict LRU item
    if (this.cache.size >= this.max_size) {
      this.evictLRU();
    }

    // Add new entry
    const entry: CacheEntry<T> = {
      data: value,
      createdAt: Date.now(),
      accessCount: 0,
      lastAccessed: Date.now()
    };

    this.cache.set(key, entry);
  }

  // Clear cache
  public clear(): void {
    this.cache.clear();
  }

  // Evict least recently used item
  private evictLRU(): void {
    if (this.cache.size === 0) return;

    // Find the least recently used item (oldest lastAccessed)
    let oldestKey: string | null = null;
    let oldestTime = Number.MAX_SAFE_INTEGER;

    const entries = Array.from(this.cache.entries());
    for (const [key, entry] of entries) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  // Check if entry is expired
  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.createdAt > this.ttl * 1000;
  }

  // Get cache statistics
  public getStats(): Record<string, any> {
    const total = this.hit_count + this.miss_count;
    return {
      [StringConstants.FIELD_SIZE]: this.cache.size,
      [StringConstants.FIELD_MAX_SIZE]: this.max_size,
      [StringConstants.FIELD_HIT_COUNT]: this.hit_count,
      [StringConstants.FIELD_MISS_COUNT]: this.miss_count,
      [StringConstants.FIELD_HIT_RATE]: total > 0 ? this.hit_count / total : 0,
      [StringConstants.FIELD_TTL]: this.ttl
    };
  }
}