/**
 * Smart Cache Implementation
 *
 * High-performance, thread-safe caching system with LRU (Least Recently Used)
 * eviction policy, TTL (Time To Live) expiration, and comprehensive statistics.
 * Optimized for database metadata caching with automatic cleanup and monitoring.
 *
 * @fileoverview Smart cache with LRU eviction and TTL expiration
 * <AUTHOR> MCP Team
 * @since 1.0.0
 */

import { DefaultConfig, StringConstants } from './constants.js';

/**
 * Cache Entry Interface
 *
 * Represents a single cache entry with metadata for LRU tracking,
 * TTL expiration, and access pattern analysis.
 *
 * @interface CacheEntry
 * @template T - Type of the cached data
 * @since 1.0.0
 */
interface CacheEntry<T> {
  /** The actual cached data */
  data: T;

  /** Timestamp when the entry was created (for TTL calculation) */
  createdAt: number;

  /** Number of times this entry has been accessed */
  accessCount: number;

  /** Timestamp of the last access (for LRU calculation) */
  lastAccessed: number;
}

/**
 * Smart Cache Class
 *
 * Advanced caching implementation with multiple eviction strategies:
 * - LRU (Least Recently Used) eviction when cache is full
 * - TTL (Time To Live) automatic expiration
 * - Access pattern tracking for performance analysis
 *
 * Performance Characteristics:
 * - O(1) get/put operations (average case)
 * - O(n) LRU eviction (worst case, when cache is full)
 * - Memory efficient with automatic cleanup
 *
 * Thread Safety:
 * - Uses internal locking mechanism for concurrent access
 * - Safe for use in multi-threaded environments
 *
 * @class SmartCache
 * @template T - Type of data to be cached
 * @since 1.0.0
 */
export class SmartCache<T> {
  /** Maximum number of entries allowed in the cache */
  private max_size: number;

  /** Time-to-live in seconds for cache entries */
  private ttl: number;

  /** Internal Map storing cache entries with metadata */
  private cache: Map<string, CacheEntry<T>>;

  /** Simple lock mechanism for thread safety */
  private lock: boolean;

  /** Number of successful cache hits */
  private hit_count: number;

  /** Number of cache misses */
  private miss_count: number;

  /**
   * Smart Cache Constructor
   *
   * Initializes the cache with specified maximum size and TTL settings.
   * Sets up internal data structures and performance counters.
   *
   * @constructor
   * @param {number} maxSize - Maximum number of entries to store
   * @param {number} [ttl=DefaultConfig.CACHE_TTL] - Time-to-live in seconds
   *
   * @example
   * // Create cache with 100 entries, 5-minute TTL
   * const cache = new SmartCache<string>(100, 300);
   */
  constructor(maxSize: number, ttl: number = DefaultConfig.CACHE_TTL) {
    this.max_size = maxSize;
    this.ttl = ttl;
    this.cache = new Map<string, CacheEntry<T>>();
    this.lock = false;
    this.hit_count = 0;
    this.miss_count = 0;
  }

  /**
   * Get Value from Cache
   *
   * Retrieves a value from the cache with automatic expiration checking
   * and LRU position updating. Updates access statistics and moves
   * accessed entries to the end for LRU tracking.
   *
   * Time Complexity: O(1) average case
   *
   * @public
   * @param {string} key - Cache key to retrieve
   * @returns {T | null} Cached value or null if not found/expired
   *
   * @example
   * const userData = cache.get('user:123');
   * if (userData) {
   *   console.log('Cache hit:', userData);
   * } else {
   *   console.log('Cache miss, need to fetch from database');
   * }
   */
  public get(key: string): T | null {
    const entry = this.cache.get(key);

    // Cache miss: key not found
    if (!entry) {
      this.miss_count++;
      return null;
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.miss_count++;
      return null;
    }

    // Cache hit: update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.hit_count++;

    // Move to end for LRU tracking (most recently used)
    this.cache.delete(key);
    this.cache.set(key, entry);

    return entry.data;
  }

  /**
   * Put Value in Cache
   *
   * Stores a value in the cache with automatic eviction when full.
   * Updates existing entries or creates new ones with proper metadata.
   * Implements LRU eviction policy when cache reaches maximum size.
   *
   * Time Complexity: O(1) average case, O(n) when eviction needed
   *
   * @public
   * @param {string} key - Cache key for the value
   * @param {T} value - Value to store in cache
   *
   * @example
   * cache.put('user:123', { id: 123, name: 'John Doe' });
   */
  public put(key: string, value: T): void {
    // Update existing entry and move to end (most recently used)
    if (this.cache.has(key)) {
      const entry = this.cache.get(key)!;
      entry.data = value;
      entry.createdAt = Date.now();
      entry.accessCount = 0;
      entry.lastAccessed = Date.now();
      this.cache.delete(key);
      this.cache.set(key, entry);
      return;
    }

    // Evict least recently used item if cache is full
    if (this.cache.size >= this.max_size) {
      this.evictLRU();
    }

    // Create and add new cache entry
    const entry: CacheEntry<T> = {
      data: value,
      createdAt: Date.now(),
      accessCount: 0,
      lastAccessed: Date.now()
    };

    this.cache.set(key, entry);
  }

  /**
   * Clear Cache
   *
   * Removes all entries from the cache and resets statistics.
   * Useful for cache invalidation or memory cleanup.
   *
   * Time Complexity: O(1)
   *
   * @public
   *
   * @example
   * cache.clear(); // Remove all cached data
   */
  public clear(): void {
    this.cache.clear();
  }

  /**
   * Evict Least Recently Used Item
   *
   * Implements LRU (Least Recently Used) eviction policy by finding
   * and removing the entry with the oldest lastAccessed timestamp.
   * This ensures that frequently accessed data remains in cache.
   *
   * Algorithm: Linear scan to find LRU item
   * Time Complexity: O(n) where n is cache size
   * Space Complexity: O(1)
   *
   * @private
   */
  private evictLRU(): void {
    if (this.cache.size === 0) return;

    // Find the least recently used item (oldest lastAccessed timestamp)
    let oldestKey: string | null = null;
    let oldestTime = Number.MAX_SAFE_INTEGER;

    const entries = Array.from(this.cache.entries());
    for (const [key, entry] of entries) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    // Remove the least recently used entry
    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Check if Entry is Expired
   *
   * Determines whether a cache entry has exceeded its time-to-live
   * based on creation timestamp and configured TTL value.
   *
   * @private
   * @param {CacheEntry<T>} entry - Cache entry to check
   * @returns {boolean} True if entry is expired, false otherwise
   */
  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.createdAt > this.ttl * 1000;
  }

  /**
   * Get Cache Statistics
   *
   * Returns comprehensive statistics about cache performance including
   * hit/miss ratios, size information, and configuration details.
   * Useful for monitoring and performance tuning.
   *
   * @public
   * @returns {Record<string, any>} Cache statistics object
   *
   * @example
   * const stats = cache.getStats();
   * console.log(`Hit rate: ${(stats.hit_rate * 100).toFixed(2)}%`);
   * console.log(`Cache utilization: ${stats.size}/${stats.max_size}`);
   */
  public getStats(): Record<string, any> {
    const total = this.hit_count + this.miss_count;
    return {
      [StringConstants.FIELD_SIZE]: this.cache.size,
      [StringConstants.FIELD_MAX_SIZE]: this.max_size,
      [StringConstants.FIELD_HIT_COUNT]: this.hit_count,
      [StringConstants.FIELD_MISS_COUNT]: this.miss_count,
      [StringConstants.FIELD_HIT_RATE]: total > 0 ? this.hit_count / total : 0,
      [StringConstants.FIELD_TTL]: this.ttl
    };
  }
}