// Enhanced Security Validator
import { DefaultConfig } from './constants.js';
export class EnhancedSecurityValidator {
    // Dangerous patterns
    static DANGEROUS_PATTERNS = [
        /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
        /\b(SYSTEM|EXEC|SHELL|xp_cmdshell)\b/i,
        /\b(UNION\s+SELECT).*(\bFROM\s+INFORMATION_SCHEMA)\b/i,
        /;\s*(DROP|DELETE|TRUNCATE|ALTER)\b/i,
        /\b(BENCHMARK|SLEEP|WAITFOR)\s*\(/i,
        /@@(version|datadir|basedir|tmpdir)/i,
    ];
    // SQL injection patterns
    static INJECTION_PATTERNS = [
        /(\s|^)('|")\s*(OR|AND)\s*(\d+|'[^']*'|\")[^><=!]*(\s)*[><=!]{1,2}.*/i,
        /('|").*(\s|^)(UNION|SELECT|INSERT|DELETE|UPDATE|DROP|CREATE|ALTER)(\s)/i,
        /\s*(OR|AND)\s+[\w'"]+\s*[><=!]+.*/i,
        /('\s*OR\s*'\d+'\s*=\s*'\d')/i,
        /("\s*OR\s*"\d+"\s*=\s*"\d")/i,
    ];
    // Comprehensive input validation
    static validateInputComprehensive(inputValue, fieldName, validationLevel = "strict") {
        // Type validation
        if (typeof inputValue !== 'string' && typeof inputValue !== 'number' && typeof inputValue !== 'boolean' && inputValue !== null && inputValue !== undefined) {
            throw new Error(`${fieldName} has invalid data type`);
        }
        // String validation
        if (typeof inputValue === 'string') {
            this.validateStringComprehensive(inputValue, fieldName, validationLevel);
        }
    }
    // Enhanced string validation
    static validateStringComprehensive(value, fieldName, level) {
        // Check control characters (except common ones like tab, newline, carriage return)
        if (value.split('').some(c => c.charCodeAt(0) < 32 && !['\t', '\n', '\r'].includes(c))) {
            throw new Error(`${fieldName} contains invalid control characters`);
        }
        // Length validation
        if (value.length > DefaultConfig.MAX_INPUT_LENGTH) {
            throw new Error(`${fieldName} exceeds maximum length limit`);
        }
        // Encoding validation
        try {
            Buffer.from(value, 'utf-8');
        }
        catch (e) {
            throw new Error(`${fieldName} contains invalid character encoding`);
        }
        if (level === "strict") {
            // Check dangerous patterns
            if (this.DANGEROUS_PATTERNS.some(pattern => pattern.test(value))) {
                throw new Error(`${fieldName} contains potentially dangerous content`);
            }
            // Check SQL injection patterns
            if (this.INJECTION_PATTERNS.some(pattern => pattern.test(value))) {
                throw new Error(`${fieldName} contains potential SQL injection attempt`);
            }
        }
        else if (level === "moderate") {
            // Only check the most dangerous patterns
            const criticalPatterns = this.DANGEROUS_PATTERNS.slice(0, 3);
            if (criticalPatterns.some(pattern => pattern.test(value))) {
                throw new Error(`${fieldName} contains dangerous content`);
            }
        }
    }
}
//# sourceMappingURL=security.js.map