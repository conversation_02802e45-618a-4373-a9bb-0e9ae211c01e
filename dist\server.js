// MySQL MCP服务器入口点
import { mcp } from './mysqlMcpServer.js';
import { StringConstants } from './constants.js';
// 主函数
async function main() {
    try {
        console.error(StringConstants.MSG_SERVER_RUNNING);
        await mcp.start();
    }
    catch (error) {
        console.error(`${StringConstants.MSG_SERVER_ERROR} ${error.message}`);
        process.exit(1);
    }
}
// 运行服务器
main();
//# sourceMappingURL=server.js.map