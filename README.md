# MySQL MCP Server - TypeScript Implementation

A TypeScript implementation of the MySQL Model Context Protocol (MCP) server built with FastMCP framework. This server provides secure database operations through intelligent caching, connection pooling, and comprehensive monitoring.

## Features

- **Secure Database Operations**: Multi-layer validation and SQL injection protection
- **Performance Optimized**: Connection pooling and intelligent caching
- **Comprehensive Monitoring**: Real-time metrics and diagnostic capabilities
- **Rate Limiting**: Adaptive rate limiting with token bucket algorithm
- **Retry Mechanisms**: Exponential backoff for resilient operations
- **Type Safety**: Full TypeScript support with strict typing

## Prerequisites

- Node.js 16+
- MySQL 5.7+
- npm or yarn

## Installation

```bash
# Install dependencies
npm install

# Build the project
npm run build
```

## Configuration

All configuration is done through environment variables:

### Database Connection
- `MYSQL_HOST` - MySQL host (default: localhost)
- `MYSQL_PORT` - MySQL port (default: 3306)
- `MYSQL_USER` - MySQL user (default: root)
- `MYSQL_PASSWORD` - MySQL password (default: "")
- `MYSQL_DATABASE` - MySQL database name (default: "")
- `MYSQL_CONNECTION_LIMIT` - Connection pool size (default: 10)
- `MYSQL_CONNECT_TIMEOUT` - Connection timeout in seconds (default: 60)
- `MYSQL_SSL` - Enable SSL (default: false)

### Security
- `MAX_QUERY_LENGTH` - Maximum query length (default: 10000)
- `MAX_RESULT_ROWS` - Maximum result rows (default: 1000)
- `QUERY_TIMEOUT` - Query timeout in seconds (default: 30)
- `RATE_LIMIT_MAX` - Rate limit maximum requests (default: 100)
- `RATE_LIMIT_WINDOW` - Rate limit window in seconds (default: 60)
- `ALLOWED_QUERY_TYPES` - Comma-separated list of allowed query types (default: SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER)

### Performance
- `SCHEMA_CACHE_SIZE` - Schema cache size (default: 128)
- `CACHE_TTL` - Cache time-to-live in seconds (default: 300)
- `SLOW_QUERY_THRESHOLD` - Slow query threshold in seconds (default: 1.0)

## Available Tools

The server exposes the following MCP tools:

- `mysql_query` - Execute custom SQL with parameters
- `mysql_show_tables` - List all tables in the database
- `mysql_describe_table` - Get table structure information
- `mysql_select_data` - Select data from a table
- `mysql_insert_data` - Insert new data into a table
- `mysql_update_data` - Update existing data in a table
- `mysql_delete_data` - Delete data from a table
- `mysql_get_schema` - Get database schema information
- `mysql_get_indexes` - Get index information
- `mysql_get_foreign_keys` - Get foreign key constraints
- `mysql_create_table` - Create a new table
- `mysql_drop_table` - Drop an existing table
- `mysql_diagnose_connection` - Comprehensive system diagnostics

## Development

```bash
# Run in development mode
npm run dev

# Run tests
npm test

# Build for production
npm run build

# Start production server
npm start
```

## Project Structure

```
src/
├── constants.ts        # Configuration constants
├── config.ts           # Configuration management
├── cache.ts            # Smart caching implementation
├── security.ts         # Security validation
├── rateLimit.ts        # Rate limiting
├── metrics.ts          # Performance metrics
├── connection.ts       # MySQL connection pooling
├── mysqlManager.ts     # Main MySQL manager
└── server.ts           # FastMCP server implementation
```

## License

MIT