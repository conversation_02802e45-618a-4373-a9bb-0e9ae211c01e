{"version": 3, "file": "rateLimit.js", "sourceRoot": "", "sources": ["../src/rateLimit.ts"], "names": [], "mappings": "AAAA,+BAA+B;AAE/B,4BAA4B;AAC5B,MAAM,OAAO,sBAAsB;IACzB,QAAQ,CAAS;IACjB,MAAM,CAAS;IACf,UAAU,CAAS,CAAC,oBAAoB;IACxC,MAAM,CAAS;IACf,UAAU,CAAS;IAE3B,YAAY,QAAgB,EAAE,UAAkB,EAAE,SAAiB,EAAE;QACnE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED,8BAA8B;IACvB,YAAY,CAAC,kBAA0B,CAAC;QAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,sCAAsC;QACtC,MAAM,OAAO,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,qBAAqB;QACrE,MAAM,WAAW,GAAG,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC;QAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QAEtB,IAAI,IAAI,CAAC,MAAM,IAAI,eAAe,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,IAAI,eAAe,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED,wBAAwB;AACxB,MAAM,OAAO,mBAAmB;IACtB,SAAS,CAAS;IAClB,MAAM,CAAS;IACf,gBAAgB,CAAS;IACzB,OAAO,CAAsC;IAErD,YAAY,SAAiB,EAAE,SAAiB,EAAE;QAChD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAkC,CAAC;IAC3D,CAAC;IAED,6CAA6C;IACtC,gBAAgB,CAAC,QAAgB,EAAE,WAAmB;QAC3D,IAAI,QAAQ,GAAG,GAAG,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;YACxC,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,oBAAoB;QACnD,CAAC;aAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,sBAAsB;QACrD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,kCAAkC;IAC3B,cAAc,CAAC,UAAkB;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEzE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,MAAM,UAAU,GAAG,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,sBAAsB,CACrD,aAAa,EACb,UAAU,EACV,IAAI,CAAC,MAAM,CACZ,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,YAAY,EAAE,CAAC;IACtD,CAAC;CACF"}