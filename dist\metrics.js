// Performance Metrics Implementation
import { StringConstants } from './constants.js';
// Time series metrics class
export class TimeSeriesMetrics {
    maxPoints;
    retentionSeconds;
    points;
    constructor(maxPoints = 1000, retentionSeconds = 3600) {
        this.maxPoints = maxPoints;
        this.retentionSeconds = retentionSeconds;
        this.points = [];
    }
    // Add metric point
    addPoint(value, labels) {
        const now = Date.now() / 1000; // Convert to seconds
        // Clean up expired data points
        this.points = this.points.filter(point => (now - point.timestamp) <= this.retentionSeconds);
        // Add new point
        this.points.push({
            timestamp: now,
            value: value,
            labels: labels
        });
        // Maintain max points limit
        if (this.points.length > this.maxPoints) {
            this.points = this.points.slice(-this.maxPoints);
        }
    }
    // Get statistics for recent time period
    getStats(sinceSeconds = 300) {
        const cutoff = (Date.now() / 1000) - sinceSeconds;
        const recentPoints = this.points.filter(p => p.timestamp >= cutoff).map(p => p.value);
        if (recentPoints.length === 0) {
            return {
                count: 0,
                avg: 0,
                min: 0,
                max: 0,
                sum: 0
            };
        }
        const sum = recentPoints.reduce((a, b) => a + b, 0);
        const avg = sum / recentPoints.length;
        const min = Math.min(...recentPoints);
        const max = Math.max(...recentPoints);
        const p95 = this.percentile(recentPoints, 0.95);
        const p99 = this.percentile(recentPoints, 0.99);
        return {
            count: recentPoints.length,
            avg: avg,
            min: min,
            max: max,
            sum: sum,
            p95: p95,
            p99: p99
        };
    }
    // Calculate percentile
    percentile(values, p) {
        if (values.length === 0)
            return 0;
        const sorted = [...values].sort((a, b) => a - b);
        const index = Math.floor(sorted.length * p);
        return sorted[Math.min(index, sorted.length - 1)];
    }
}
// Enhanced metrics manager
export class EnhancedMetricsManager {
    queryTimes;
    errorCounts;
    cacheHitRates;
    systemMetrics;
    alertCallbacks = [];
    alertRules;
    shutdownEvent = false;
    metricsInterval = null;
    constructor() {
        this.queryTimes = new TimeSeriesMetrics();
        this.errorCounts = new TimeSeriesMetrics();
        this.cacheHitRates = new TimeSeriesMetrics();
        this.systemMetrics = new TimeSeriesMetrics();
        this.alertRules = this.setupDefaultAlertRules();
    }
    // Start monitoring
    startMonitoring() {
        if (!this.metricsInterval) {
            this.metricsInterval = setInterval(() => {
                this.collectSystemMetrics();
            }, 30000); // Collect every 30 seconds
        }
    }
    // Stop monitoring
    stopMonitoring() {
        this.shutdownEvent = true;
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
            this.metricsInterval = null;
        }
    }
    // Record query time
    recordQueryTime(duration, queryType) {
        const labels = queryType ? { query_type: queryType } : undefined;
        this.queryTimes.addPoint(duration, labels);
        // Check for slow query alerts
        if (duration > 2.0) { // Slow query over 2 seconds
            this.triggerAlert("Slow Query", { duration: duration, query_type: queryType });
        }
    }
    // Record error
    recordError(errorType, severity = "medium") {
        this.errorCounts.addPoint(1, { error_type: errorType, severity: severity });
        if (severity === "high") {
            this.triggerAlert("High Severity Error", { error_type: errorType });
        }
    }
    // Record cache hit rate
    recordCacheHitRate(hitRate, cacheType) {
        const labels = cacheType ? { cache_type: cacheType } : undefined;
        this.cacheHitRates.addPoint(hitRate, labels);
        // Check for low hit rate alerts
        if (hitRate < 0.6) { // Hit rate below 60%
            this.triggerAlert("Low Cache Hit Rate", { hit_rate: hitRate, cache_type: cacheType });
        }
    }
    // Collect system metrics
    collectSystemMetrics() {
        // In a real implementation, we would collect actual system metrics
        // For now, we'll just simulate this
        try {
            // This would be where we collect CPU and memory usage
            // Since we don't have access to system metrics in this environment,
            // we'll skip the actual collection
        }
        catch (e) {
            // Don't let metrics collection affect system operation
        }
    }
    // Setup default alert rules
    setupDefaultAlertRules() {
        return {
            "Slow Query": { threshold: 2.0, window: 300, count: 5 },
            "High Error Rate": { threshold: 0.05, window: 300 },
            "Low Cache Hit Rate": { threshold: 0.6, window: 600 }
        };
    }
    // Add alert callback
    addAlertCallback(callback) {
        this.alertCallbacks.push(callback);
    }
    // Trigger alert
    triggerAlert(alertType, context) {
        for (const callback of this.alertCallbacks) {
            try {
                callback(alertType, context);
            }
            catch (e) {
                // Don't let alert failures affect system
            }
        }
    }
    // Get comprehensive metrics
    getComprehensiveMetrics() {
        return {
            query_performance: this.queryTimes.getStats(),
            error_statistics: this.errorCounts.getStats(),
            cache_performance: this.cacheHitRates.getStats(),
            system_metrics: this.systemMetrics.getStats(),
            alert_rules: this.alertRules
        };
    }
}
// Performance metrics class (legacy compatibility)
export class PerformanceMetrics {
    queryCount = 0;
    totalQueryTime = 0.0;
    slowQueryCount = 0;
    errorCount = 0;
    cacheHits = 0;
    cacheMisses = 0;
    connectionPoolHits = 0;
    connectionPoolWaits = 0;
    // Get average query time
    getAvgQueryTime() {
        return this.totalQueryTime / Math.max(this.queryCount, 1);
    }
    // Get cache hit rate
    getCacheHitRate() {
        const total = this.cacheHits + this.cacheMisses;
        return this.cacheHits / Math.max(total, 1);
    }
    // Get error rate
    getErrorRate() {
        return this.errorCount / Math.max(this.queryCount, 1);
    }
    // Convert to object
    toObject() {
        return {
            [StringConstants.FIELD_QUERY_COUNT]: this.queryCount,
            [StringConstants.FIELD_AVG_QUERY_TIME]: this.getAvgQueryTime(),
            [StringConstants.FIELD_SLOW_QUERY_COUNT]: this.slowQueryCount,
            [StringConstants.FIELD_ERROR_COUNT]: this.errorCount,
            [StringConstants.FIELD_ERROR_RATE]: this.getErrorRate(),
            [StringConstants.FIELD_CACHE_HIT_RATE]: this.getCacheHitRate(),
            [StringConstants.FIELD_CONNECTION_POOL_HITS]: this.connectionPoolHits,
            [StringConstants.FIELD_CONNECTION_POOL_WAITS]: this.connectionPoolWaits
        };
    }
}
//# sourceMappingURL=metrics.js.map