export declare class TimeSeriesMetrics {
    private maxPoints;
    private retentionSeconds;
    private points;
    constructor(maxPoints?: number, retentionSeconds?: number);
    addPoint(value: number, labels?: Record<string, string>): void;
    getStats(sinceSeconds?: number): Record<string, number>;
    private percentile;
}
export declare class EnhancedMetricsManager {
    queryTimes: TimeSeriesMetrics;
    errorCounts: TimeSeriesMetrics;
    cacheHitRates: TimeSeriesMetrics;
    systemMetrics: TimeSeriesMetrics;
    private alertCallbacks;
    private alertRules;
    private shutdownEvent;
    private metricsInterval;
    constructor();
    startMonitoring(): void;
    stopMonitoring(): void;
    recordQueryTime(duration: number, queryType?: string): void;
    recordError(errorType: string, severity?: string): void;
    recordCacheHitRate(hitRate: number, cacheType?: string): void;
    private collectSystemMetrics;
    private setupDefaultAlertRules;
    addAlertCallback(callback: (alertType: string, context: Record<string, any>) => void): void;
    private triggerAlert;
    getComprehensiveMetrics(): Record<string, any>;
}
export declare class PerformanceMetrics {
    queryCount: number;
    totalQueryTime: number;
    slowQueryCount: number;
    errorCount: number;
    cacheHits: number;
    cacheMisses: number;
    connectionPoolHits: number;
    connectionPoolWaits: number;
    getAvgQueryTime(): number;
    getCacheHitRate(): number;
    getErrorRate(): number;
    toObject(): Record<string, any>;
}
//# sourceMappingURL=metrics.d.ts.map