import { EventStore } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { JSONRPCMessage, ServerCapabilities } from '@modelcontextprotocol/sdk/types.js';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import http from 'http';
import { SSEClientTransportOptions } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransportOptions } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';

/**
 * This is a copy of the InMemoryEventStore from the typescript-sdk
 * https://github.com/modelcontextprotocol/typescript-sdk/blob/main/src/inMemoryEventStore.ts
 */

/**
 * Simple in-memory implementation of the EventStore interface for resumability
 * This is primarily intended for examples and testing, not for production use
 * where a persistent storage solution would be more appropriate.
 */
declare class InMemoryEventStore implements EventStore {
    private events;
    /**
     * Replays events that occurred after a specific event ID
     * Implements EventStore.replayEventsAfter
     */
    replayEventsAfter(lastEventId: string, { send, }: {
        send: (eventId: string, message: JSONRPCMessage) => Promise<void>;
    }): Promise<string>;
    /**
     * Stores an event with a generated event ID
     * Implements EventStore.storeEvent
     */
    storeEvent(streamId: string, message: JSONRPCMessage): Promise<string>;
    /**
     * Generates a unique event ID for a given stream ID
     */
    private generateEventId;
    /**
     * Extracts the stream ID from an event ID
     */
    private getStreamIdFromEventId;
}

declare const proxyServer: ({ client, server, serverCapabilities, }: {
    client: Client;
    server: Server;
    serverCapabilities: ServerCapabilities;
}) => Promise<void>;

type SSEServer = {
    close: () => Promise<void>;
};
type ServerLike = {
    close: Server["close"];
    connect: Server["connect"];
};
declare const startHTTPServer: <T extends ServerLike>({ createServer, eventStore, onClose, onConnect, onUnhandledRequest, port, sseEndpoint, streamEndpoint, }: {
    createServer: (request: http.IncomingMessage) => Promise<T>;
    eventStore?: EventStore;
    onClose?: (server: T) => void;
    onConnect?: (server: T) => void;
    onUnhandledRequest?: (req: http.IncomingMessage, res: http.ServerResponse) => Promise<void>;
    port: number;
    sseEndpoint?: null | string;
    streamEndpoint?: null | string;
}) => Promise<SSEServer>;

declare enum ServerType {
    HTTPStream = "HTTPStream",
    SSE = "SSE"
}
declare const startStdioServer: ({ initStdioServer, initStreamClient, serverType, transportOptions, url, }: {
    initStdioServer?: () => Promise<Server>;
    initStreamClient?: () => Promise<Client>;
    serverType: ServerType;
    transportOptions?: SSEClientTransportOptions | StreamableHTTPClientTransportOptions;
    url: string;
}) => Promise<Server>;

type TransportEvent = {
    error: Error;
    type: "onerror";
} | {
    message: JSONRPCMessage;
    type: "onmessage";
} | {
    message: JSONRPCMessage;
    type: "send";
} | {
    type: "close";
} | {
    type: "onclose";
} | {
    type: "start";
};
declare const tapTransport: (transport: Transport, eventHandler: (event: TransportEvent) => void) => Transport;

export { InMemoryEventStore, ServerType, proxyServer, startHTTPServer, startStdioServer, tapTransport };
