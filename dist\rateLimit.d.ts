export declare class TokenBucketRateLimiter {
    private capacity;
    private tokens;
    private refillRate;
    private window;
    private lastRefill;
    constructor(capacity: number, refillRate: number, window?: number);
    allowRequest(tokensRequested?: number): boolean;
}
export declare class AdaptiveRateLimiter {
    private baseLimit;
    private window;
    private systemLoadFactor;
    private buckets;
    constructor(baseLimit: number, window?: number);
    updateSystemLoad(cpuUsage: number, memoryUsage: number): void;
    checkRateLimit(identifier: string): boolean;
}
//# sourceMappingURL=rateLimit.d.ts.map