export declare const MySQLErrorCodes: {
    readonly ACCESS_DENIED: 1045;
    readonly ACCESS_DENIED_FOR_USER: 1044;
    readonly TABLE_ACCESS_DENIED: 1142;
    readonly COLUMN_ACCESS_DENIED: 1143;
    readonly UNKNOWN_DATABASE: 1049;
    readonly TABLE_DOESNT_EXIST: 1146;
    readonly UNKNOWN_COLUMN: 1054;
    readonly UNKNOWN_TABLE: 1109;
    readonly DUPLICATE_ENTRY: 1062;
    readonly DUPLICATE_ENTRY_WITH_KEY_NAME: 1586;
    readonly DUPLICATE_KEY_NAME: 1557;
    readonly PARSE_ERROR: 1064;
    readonly SYNTAX_ERROR: 1149;
    readonly PARSE_ERROR_NEAR: 1065;
    readonly CANT_CONNECT_TO_SERVER: 2003;
    readonly LOST_CONNECTION: 2013;
    readonly SERVER_HAS_GONE_AWAY: 2006;
};
export declare const DefaultConfig: {
    readonly MYSQL_PORT: 3306;
    readonly CONNECTION_LIMIT: 10;
    readonly MIN_CONNECTIONS: 2;
    readonly CONNECT_TIMEOUT: 60;
    readonly IDLE_TIMEOUT: 60;
    readonly MAX_QUERY_LENGTH: 10000;
    readonly MAX_RESULT_ROWS: 1000;
    readonly QUERY_TIMEOUT: 30;
    readonly RATE_LIMIT_WINDOW: 60;
    readonly RATE_LIMIT_MAX: 100;
    readonly MAX_INPUT_LENGTH: 1000;
    readonly MAX_TABLE_NAME_LENGTH: 64;
    readonly SCHEMA_CACHE_SIZE: 128;
    readonly TABLE_EXISTS_CACHE_SIZE: 64;
    readonly INDEX_CACHE_SIZE: 64;
    readonly CACHE_TTL: 300;
    readonly HEALTH_CHECK_INTERVAL: 30;
    readonly CONNECTION_MAX_AGE: 3600;
    readonly METRICS_WINDOW_SIZE: 1000;
    readonly SLOW_QUERY_THRESHOLD: 1;
    readonly RECONNECT_ATTEMPTS: 3;
    readonly RECONNECT_DELAY: 1;
    readonly MAX_RETRY_ATTEMPTS: 3;
    readonly BATCH_SIZE: 1000;
    readonly MAX_LOG_DETAIL_LENGTH: 100;
};
export declare const StringConstants: {
    readonly DEFAULT_HOST: "localhost";
    readonly DEFAULT_USER: "root";
    readonly DEFAULT_PASSWORD: "";
    readonly DEFAULT_DATABASE: "";
    readonly POOL_NAME: "mysql_mcp_pool";
    readonly CHARSET: "utf8mb4";
    readonly SQL_MODE: "TRADITIONAL";
    readonly ENV_MYSQL_HOST: "MYSQL_HOST";
    readonly ENV_MYSQL_PORT: "MYSQL_PORT";
    readonly ENV_MYSQL_USER: "MYSQL_USER";
    readonly ENV_MYSQL_PASSWORD: "MYSQL_PASSWORD";
    readonly ENV_MYSQL_DATABASE: "MYSQL_DATABASE";
    readonly ENV_MYSQL_SSL: "MYSQL_SSL";
    readonly ENV_CONNECTION_LIMIT: "MYSQL_CONNECTION_LIMIT";
    readonly ENV_CONNECT_TIMEOUT: "MYSQL_CONNECT_TIMEOUT";
    readonly ENV_IDLE_TIMEOUT: "MYSQL_IDLE_TIMEOUT";
    readonly ENV_ALLOWED_QUERY_TYPES: "ALLOWED_QUERY_TYPES";
    readonly ENV_MAX_QUERY_LENGTH: "MAX_QUERY_LENGTH";
    readonly ENV_MAX_RESULT_ROWS: "MAX_RESULT_ROWS";
    readonly ENV_QUERY_TIMEOUT: "QUERY_TIMEOUT";
    readonly ENV_RATE_LIMIT_WINDOW: "RATE_LIMIT_WINDOW";
    readonly ENV_RATE_LIMIT_MAX: "RATE_LIMIT_MAX";
    readonly SQL_SELECT: "SELECT";
    readonly SQL_SHOW: "SHOW";
    readonly SQL_DESCRIBE: "DESCRIBE";
    readonly SQL_INSERT: "INSERT";
    readonly SQL_UPDATE: "UPDATE";
    readonly SQL_DELETE: "DELETE";
    readonly SQL_CREATE: "CREATE";
    readonly SQL_DROP: "DROP";
    readonly SQL_ALTER: "ALTER";
    readonly DEFAULT_ALLOWED_QUERY_TYPES: "SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER";
    readonly DANGEROUS_PATTERNS: readonly ["--", "/*", "*/", "xp_", "sp_"];
    readonly ERROR_CATEGORY_ACCESS_DENIED: "access_denied";
    readonly ERROR_CATEGORY_OBJECT_NOT_FOUND: "object_not_found";
    readonly ERROR_CATEGORY_CONSTRAINT_VIOLATION: "constraint_violation";
    readonly ERROR_CATEGORY_SYNTAX_ERROR: "syntax_error";
    readonly ERROR_CATEGORY_CONNECTION_ERROR: "connection_error";
    readonly ERROR_CATEGORY_UNKNOWN: "unknown";
    readonly SEVERITY_HIGH: "high";
    readonly SEVERITY_MEDIUM: "medium";
    readonly SEVERITY_LOW: "low";
    readonly LOG_EVENT_SECURITY: "[SECURITY]";
    readonly MSG_DATABASE_ACCESS_DENIED: "Database access denied, please check username and password";
    readonly MSG_DATABASE_OBJECT_NOT_FOUND: "Database object not found";
    readonly MSG_DATABASE_CONSTRAINT_VIOLATION: "Data constraint violation";
    readonly MSG_DATABASE_SYNTAX_ERROR: "SQL syntax error";
    readonly MSG_DATABASE_CONNECTION_ERROR: "Database connection error";
    readonly MSG_MYSQL_CONNECTION_POOL_FAILED: "MySQL connection pool creation failed";
    readonly MSG_MYSQL_CONNECTION_ERROR: "MySQL connection error";
    readonly MSG_MYSQL_QUERY_ERROR: "MySQL query error";
    readonly MSG_RATE_LIMIT_EXCEEDED: "Rate limit exceeded. Please try again later.";
    readonly MSG_QUERY_TOO_LONG: "Query exceeds maximum allowed length";
    readonly MSG_PROHIBITED_OPERATIONS: "Query contains prohibited operations";
    readonly MSG_QUERY_TYPE_NOT_ALLOWED: "Query type '{query_type}' is not allowed";
    readonly MSG_INVALID_TABLE_NAME: "Invalid table name format";
    readonly MSG_TABLE_NAME_TOO_LONG: "Table name exceeds maximum length";
    readonly MSG_INVALID_CHARACTER: "Invalid character in {field_name}";
    readonly MSG_INPUT_TOO_LONG: "{field_name} exceeds maximum length";
    readonly MSG_DANGEROUS_CONTENT: "Potentially dangerous content in {field_name}";
    readonly STATUS_SUCCESS: "success";
    readonly STATUS_FAILED: "failed";
    readonly STATUS_NOT_INITIALIZED: "not_initialized";
    readonly STATUS_ERROR: "error";
    readonly STATUS_KEY: "status";
    readonly ERROR_KEY: "error";
    readonly NULL_BYTE: "\0";
    readonly TRUE_STRING: "true";
    readonly SERVER_NAME: "mysql-mcp-server";
    readonly MSG_FASTMCP_NOT_INSTALLED: "Error: FastMCP not installed. Please install with: npm install fastmcp";
    readonly MSG_ERROR_DURING_CLEANUP: "Error during cleanup:";
    readonly MSG_SIGNAL_RECEIVED: "Signal received";
    readonly MSG_GRACEFUL_SHUTDOWN: "Gracefully shutting down...";
    readonly MSG_SERVER_RUNNING: "MySQL MCP Server (FastMCP) running on stdio";
    readonly MSG_SERVER_ERROR: "Server error:";
    readonly MSG_QUERY_FAILED: "Query failed:";
    readonly MSG_SHOW_TABLES_FAILED: "Show tables failed:";
    readonly MSG_DESCRIBE_TABLE_FAILED: "Describe table failed:";
    readonly MSG_GET_METRICS_FAILED: "Get performance metrics failed:";
    readonly MSG_SELECT_DATA_FAILED: "Select data failed:";
    readonly MSG_INSERT_DATA_FAILED: "Insert data failed:";
    readonly MSG_UPDATE_DATA_FAILED: "Update data failed:";
    readonly MSG_DELETE_DATA_FAILED: "Delete data failed:";
    readonly MSG_GET_SCHEMA_FAILED: "Get schema failed:";
    readonly MSG_GET_INDEXES_FAILED: "Get indexes failed:";
    readonly MSG_GET_FOREIGN_KEYS_FAILED: "Get foreign keys failed:";
    readonly MSG_CREATE_TABLE_FAILED: "Create table failed:";
    readonly MSG_DROP_TABLE_FAILED: "Drop table failed:";
    readonly MSG_DIAGNOSE_FAILED: "Diagnose failed:";
    readonly MSG_GET_POOL_STATUS_FAILED: "Get connection pool status failed:";
    readonly MSG_FAILED_TO_INIT_POOL: "Failed to initialize connection pool:";
    readonly SUCCESS_KEY: "success";
    readonly SQL_IF_EXISTS: "IF EXISTS";
    readonly FIELD_QUERY_COUNT: "query_count";
    readonly FIELD_AVG_QUERY_TIME: "avg_query_time";
    readonly FIELD_SLOW_QUERY_COUNT: "slow_query_count";
    readonly FIELD_ERROR_COUNT: "error_count";
    readonly FIELD_ERROR_RATE: "error_rate";
    readonly FIELD_CACHE_HIT_RATE: "cache_hit_rate";
    readonly FIELD_CONNECTION_POOL_HITS: "connection_pool_hits";
    readonly FIELD_CONNECTION_POOL_WAITS: "connection_pool_waits";
    readonly FIELD_SIZE: "size";
    readonly FIELD_MAX_SIZE: "max_size";
    readonly FIELD_HIT_COUNT: "hit_count";
    readonly FIELD_MISS_COUNT: "miss_count";
    readonly FIELD_HIT_RATE: "hit_rate";
    readonly FIELD_TTL: "ttl";
    readonly FIELD_POOL_NAME: "pool_name";
    readonly FIELD_POOL_SIZE: "pool_size";
    readonly FIELD_AVAILABLE_CONNECTIONS: "available_connections";
    readonly FIELD_CONNECTION_STATS: "connection_stats";
    readonly FIELD_HEALTH_CHECK_ACTIVE: "health_check_active";
    readonly FIELD_POOL_HITS: "pool_hits";
    readonly FIELD_POOL_WAITS: "pool_waits";
    readonly SECTION_PERFORMANCE: "performance";
    readonly SECTION_CACHE_STATS: "cache_stats";
    readonly SECTION_CONNECTION_POOL: "connection_pool";
    readonly SECTION_SCHEMA_CACHE: "schema_cache";
    readonly SECTION_TABLE_EXISTS_CACHE: "table_exists_cache";
    readonly SECTION_INDEX_CACHE: "index_cache";
    readonly FIELD_HOST: "host";
    readonly FIELD_PORT: "port";
    readonly FIELD_DATABASE: "database";
    readonly FIELD_CONNECTION_LIMIT: "connection_limit";
    readonly FIELD_CONNECT_TIMEOUT: "connect_timeout";
    readonly FIELD_SSL_ENABLED: "ssl_enabled";
    readonly FIELD_CONNECTION_POOL_STATUS: "connection_pool_status";
    readonly FIELD_CONFIG: "config";
    readonly FIELD_SECURITY_CONFIG: "security_config";
    readonly FIELD_PERFORMANCE_METRICS: "performance_metrics";
    readonly FIELD_CONNECTION_TEST: "connection_test";
    readonly FIELD_MAX_QUERY_LENGTH: "max_query_length";
    readonly FIELD_MAX_RESULT_ROWS: "max_result_rows";
    readonly FIELD_RATE_LIMIT_MAX: "rate_limit_max";
    readonly FIELD_ALLOWED_QUERY_TYPES: "allowed_query_types";
    readonly FIELD_RESULT: "result";
};
//# sourceMappingURL=constants.d.ts.map