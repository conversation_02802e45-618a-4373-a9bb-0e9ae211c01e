import { PoolConnection } from 'mysql2/promise';
import { DatabaseConfig } from './config.js';
export declare class ConnectionPool {
    private config;
    private pool;
    private healthCheckInterval;
    private shutdownEvent;
    private connectionStats;
    constructor(config: DatabaseConfig);
    initialize(): Promise<void>;
    private preCreateConnections;
    private startHealthCheck;
    private performHealthCheck;
    getConnection(): Promise<PoolConnection>;
    getStats(): Record<string, any>;
    close(): Promise<void>;
}
//# sourceMappingURL=connection.d.ts.map