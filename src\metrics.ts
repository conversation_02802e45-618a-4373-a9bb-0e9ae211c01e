/**
 * 性能指标实现
 *
 * 综合性能监控系统，具有时间序列数据收集、统计分析和告警功能。
 * 提供系统性能、查询执行、缓存效率和资源利用率的实时洞察。
 *
 * @fileoverview 高级性能指标和监控系统
 * <AUTHOR> MCP 团队
 * @since 1.0.0
 */

import { StringConstants } from './constants.js';

/**
 * 指标点接口
 *
 * 表示时间序列中的单个数据点，包含时间戳、值和用于维度分析的可选标签。
 *
 * @interface MetricPoint
 * @since 1.0.0
 */
interface MetricPoint {
  /** 记录指标时的 Unix 时间戳（秒） */
  timestamp: number;

  /** 指标的数值 */
  value: number;

  /** 用于维度分析的可选标签（例如：query_type、cache_type） */
  labels?: Record<string, string>;
}

/**
 * Time Series Metrics Class
 *
 * Manages time-series data with automatic retention, statistical analysis,
 * and efficient storage. Provides percentile calculations, trend analysis,
 * and memory-efficient data management.
 *
 * Features:
 * - Automatic data retention based on time and count limits
 * - Statistical analysis (min, max, avg, percentiles)
 * - Memory-efficient circular buffer implementation
 * - Label-based dimensional analysis
 * - Real-time data aggregation
 *
 * @class TimeSeriesMetrics
 * @since 1.0.0
 */
export class TimeSeriesMetrics {
  /** Maximum number of data points to retain */
  private maxPoints: number;

  /** Data retention period in seconds */
  private retentionSeconds: number;

  /** Array of metric data points */
  private points: MetricPoint[];

  /**
   * Time Series Metrics Constructor
   *
   * Initializes the time series with specified retention limits.
   * Data is automatically cleaned up based on age and count.
   *
   * @constructor
   * @param {number} [maxPoints=1000] - Maximum number of points to retain
   * @param {number} [retentionSeconds=3600] - Data retention period in seconds
   *
   * @example
   * // Create metrics with 1-hour retention, max 500 points
   * const metrics = new TimeSeriesMetrics(500, 3600);
   */
  constructor(maxPoints: number = 1000, retentionSeconds: number = 3600) {
    this.maxPoints = maxPoints;
    this.retentionSeconds = retentionSeconds;
    this.points = [];
  }

  /**
   * Add Metric Point
   *
   * Adds a new data point to the time series with automatic cleanup
   * of expired data. Maintains both time-based and count-based limits.
   *
   * @public
   * @param {number} value - Numeric value to record
   * @param {Record<string, string>} [labels] - Optional labels for dimensional analysis
   *
   * @example
   * // Record query execution time
   * metrics.addPoint(1.25, { query_type: 'SELECT', table: 'users' });
   *
   * @example
   * // Record simple metric
   * metrics.addPoint(42);
   */
  public addPoint(value: number, labels?: Record<string, string>): void {
    const now = Date.now() / 1000; // Convert to seconds

    // Clean up expired data points based on retention policy
    this.points = this.points.filter(point => (now - point.timestamp) <= this.retentionSeconds);

    // Add new data point
    this.points.push({
      timestamp: now,
      value: value,
      labels: labels
    });

    // Maintain maximum points limit (circular buffer behavior)
    if (this.points.length > this.maxPoints) {
      this.points = this.points.slice(-this.maxPoints);
    }
  }

  /**
   * Get Statistics for Recent Time Period
   *
   * Calculates comprehensive statistics for data points within the specified
   * time window. Includes basic statistics and percentile calculations.
   *
   * @public
   * @param {number} [sinceSeconds=300] - Time window in seconds (default: 5 minutes)
   * @returns {Record<string, number>} Statistical summary of recent data
   *
   * @example
   * // Get stats for last 10 minutes
   * const stats = metrics.getStats(600);
   * console.log(`Average: ${stats.avg}, P95: ${stats.p95}`);
   */
  public getStats(sinceSeconds: number = 300): Record<string, number> {
    const cutoff = (Date.now() / 1000) - sinceSeconds;
    const recentPoints = this.points.filter(p => p.timestamp >= cutoff).map(p => p.value);

    // Handle empty dataset
    if (recentPoints.length === 0) {
      return {
        count: 0,
        avg: 0,
        min: 0,
        max: 0,
        sum: 0
      };
    }

    // Calculate basic statistics
    const sum = recentPoints.reduce((a, b) => a + b, 0);
    const avg = sum / recentPoints.length;
    const min = Math.min(...recentPoints);
    const max = Math.max(...recentPoints);
    const p95 = this.percentile(recentPoints, 0.95);
    const p99 = this.percentile(recentPoints, 0.99);

    return {
      count: recentPoints.length,
      avg: avg,
      min: min,
      max: max,
      sum: sum,
      p95: p95,
      p99: p99
    };
  }

  /**
   * Calculate Percentile
   *
   * Calculates the specified percentile value from a dataset using
   * the nearest-rank method for accurate percentile estimation.
   *
   * @private
   * @param {number[]} values - Array of numeric values
   * @param {number} p - Percentile to calculate (0.0 to 1.0)
   * @returns {number} Percentile value
   */
  private percentile(values: number[], p: number): number {
    if (values.length === 0) return 0;
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.floor(sorted.length * p);
    return sorted[Math.min(index, sorted.length - 1)];
  }
}

/**
 * Enhanced Metrics Manager
 *
 * Advanced metrics management system with time-series data collection,
 * alerting, and comprehensive performance monitoring. Manages multiple
 * metric types with automatic system monitoring and alert generation.
 *
 * Features:
 * - Multi-dimensional time-series metrics
 * - Configurable alerting system with callbacks
 * - Automatic system resource monitoring
 * - Performance trend analysis
 * - Real-time metric aggregation
 * - Memory-efficient data retention
 *
 * @class EnhancedMetricsManager
 * @since 1.0.0
 */
export class EnhancedMetricsManager {
  /** Query execution time metrics */
  public queryTimes: TimeSeriesMetrics;

  /** Error occurrence metrics */
  public errorCounts: TimeSeriesMetrics;

  /** Cache hit rate metrics */
  public cacheHitRates: TimeSeriesMetrics;

  /** System resource utilization metrics */
  public systemMetrics: TimeSeriesMetrics;

  /** Array of alert callback functions */
  private alertCallbacks: Array<(alertType: string, context: Record<string, any>) => void> = [];

  /** Alert rule configurations */
  private alertRules: Record<string, Record<string, any>>;

  /** Shutdown event flag for graceful termination */
  private shutdownEvent: boolean = false;

  /** System monitoring interval timer */
  private metricsInterval: NodeJS.Timeout | null = null;

  /**
   * Enhanced Metrics Manager Constructor
   *
   * Initializes all metric collectors and sets up default alert rules.
   * Creates time-series instances for different metric types.
   *
   * @constructor
   */
  constructor() {
    this.queryTimes = new TimeSeriesMetrics();
    this.errorCounts = new TimeSeriesMetrics();
    this.cacheHitRates = new TimeSeriesMetrics();
    this.systemMetrics = new TimeSeriesMetrics();
    this.alertRules = this.setupDefaultAlertRules();
  }

  /**
   * Start Monitoring
   *
   * Begins automatic system metrics collection at regular intervals.
   * Collects CPU, memory, and other system metrics for performance analysis.
   *
   * @public
   */
  public startMonitoring(): void {
    if (!this.metricsInterval) {
      this.metricsInterval = setInterval(() => {
        this.collectSystemMetrics();
      }, 30000); // Collect every 30 seconds
    }
  }

  /**
   * Stop Monitoring
   *
   * Stops automatic system metrics collection and cleans up resources.
   * Should be called during application shutdown.
   *
   * @public
   */
  public stopMonitoring(): void {
    this.shutdownEvent = true;
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }
  }

  /**
   * Record Query Time
   *
   * Records query execution time with optional query type labeling.
   * Automatically triggers alerts for slow queries.
   *
   * @public
   * @param {number} duration - Query execution time in seconds
   * @param {string} [queryType] - Optional query type for categorization
   *
   * @example
   * manager.recordQueryTime(1.25, 'SELECT');
   */
  public recordQueryTime(duration: number, queryType?: string): void {
    const labels = queryType ? { query_type: queryType } : undefined;
    this.queryTimes.addPoint(duration, labels);

    // Automatic slow query detection and alerting
    if (duration > 2.0) { // Slow query threshold: 2 seconds
      this.triggerAlert("Slow Query", { duration: duration, query_type: queryType });
    }
  }

  /**
   * Record Error
   *
   * Records error occurrences with type and severity classification.
   * Automatically triggers alerts for high-severity errors.
   *
   * @public
   * @param {string} errorType - Type/category of the error
   * @param {string} [severity="medium"] - Error severity level
   *
   * @example
   * manager.recordError('connection_timeout', 'high');
   */
  public recordError(errorType: string, severity: string = "medium"): void {
    this.errorCounts.addPoint(1, { error_type: errorType, severity: severity });

    // Automatic high-severity error alerting
    if (severity === "high") {
      this.triggerAlert("High Severity Error", { error_type: errorType });
    }
  }

  /**
   * Record Cache Hit Rate
   *
   * Records cache performance metrics with optional cache type labeling.
   * Automatically triggers alerts for poor cache performance.
   *
   * @public
   * @param {number} hitRate - Cache hit rate (0.0 to 1.0)
   * @param {string} [cacheType] - Optional cache type for categorization
   *
   * @example
   * manager.recordCacheHitRate(0.85, 'schema_cache');
   */
  public recordCacheHitRate(hitRate: number, cacheType?: string): void {
    const labels = cacheType ? { cache_type: cacheType } : undefined;
    this.cacheHitRates.addPoint(hitRate, labels);

    // Automatic low cache hit rate alerting
    if (hitRate < 0.6) { // Hit rate threshold: 60%
      this.triggerAlert("Low Cache Hit Rate", { hit_rate: hitRate, cache_type: cacheType });
    }
  }

  // Collect system metrics
  private collectSystemMetrics(): void {
    // In a real implementation, we would collect actual system metrics
    // For now, we'll just simulate this
    try {
      // This would be where we collect CPU and memory usage
      // Since we don't have access to system metrics in this environment,
      // we'll skip the actual collection
    } catch (e) {
      // Don't let metrics collection affect system operation
    }
  }

  // Setup default alert rules
  private setupDefaultAlertRules(): Record<string, Record<string, any>> {
    return {
      "Slow Query": { threshold: 2.0, window: 300, count: 5 },
      "High Error Rate": { threshold: 0.05, window: 300 },
      "Low Cache Hit Rate": { threshold: 0.6, window: 600 }
    };
  }

  // Add alert callback
  public addAlertCallback(callback: (alertType: string, context: Record<string, any>) => void): void {
    this.alertCallbacks.push(callback);
  }

  // Trigger alert
  private triggerAlert(alertType: string, context: Record<string, any>): void {
    for (const callback of this.alertCallbacks) {
      try {
        callback(alertType, context);
      } catch (e) {
        // Don't let alert failures affect system
      }
    }
  }

  // Get comprehensive metrics
  public getComprehensiveMetrics(): Record<string, any> {
    return {
      query_performance: this.queryTimes.getStats(),
      error_statistics: this.errorCounts.getStats(),
      cache_performance: this.cacheHitRates.getStats(),
      system_metrics: this.systemMetrics.getStats(),
      alert_rules: this.alertRules
    };
  }
}

// Performance metrics class (legacy compatibility)
export class PerformanceMetrics {
  public queryCount: number = 0;
  public totalQueryTime: number = 0.0;
  public slowQueryCount: number = 0;
  public errorCount: number = 0;
  public cacheHits: number = 0;
  public cacheMisses: number = 0;
  public connectionPoolHits: number = 0;
  public connectionPoolWaits: number = 0;

  // Get average query time
  public getAvgQueryTime(): number {
    return this.totalQueryTime / Math.max(this.queryCount, 1);
  }

  // Get cache hit rate
  public getCacheHitRate(): number {
    const total = this.cacheHits + this.cacheMisses;
    return this.cacheHits / Math.max(total, 1);
  }

  // Get error rate
  public getErrorRate(): number {
    return this.errorCount / Math.max(this.queryCount, 1);
  }

  // Convert to object
  public toObject(): Record<string, any> {
    return {
      [StringConstants.FIELD_QUERY_COUNT]: this.queryCount,
      [StringConstants.FIELD_AVG_QUERY_TIME]: this.getAvgQueryTime(),
      [StringConstants.FIELD_SLOW_QUERY_COUNT]: this.slowQueryCount,
      [StringConstants.FIELD_ERROR_COUNT]: this.errorCount,
      [StringConstants.FIELD_ERROR_RATE]: this.getErrorRate(),
      [StringConstants.FIELD_CACHE_HIT_RATE]: this.getCacheHitRate(),
      [StringConstants.FIELD_CONNECTION_POOL_HITS]: this.connectionPoolHits,
      [StringConstants.FIELD_CONNECTION_POOL_WAITS]: this.connectionPoolWaits
    };
  }
}