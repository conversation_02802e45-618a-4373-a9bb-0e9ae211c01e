// Performance Metrics Implementation
import { StringConstants } from './constants.js';

// Metric point interface
interface MetricPoint {
  timestamp: number;
  value: number;
  labels?: Record<string, string>;
}

// Time series metrics class
export class TimeSeriesMetrics {
  private maxPoints: number;
  private retentionSeconds: number;
  private points: MetricPoint[];
  
  constructor(maxPoints: number = 1000, retentionSeconds: number = 3600) {
    this.maxPoints = maxPoints;
    this.retentionSeconds = retentionSeconds;
    this.points = [];
  }

  // Add metric point
  public addPoint(value: number, labels?: Record<string, string>): void {
    const now = Date.now() / 1000; // Convert to seconds
    
    // Clean up expired data points
    this.points = this.points.filter(point => (now - point.timestamp) <= this.retentionSeconds);
    
    // Add new point
    this.points.push({
      timestamp: now,
      value: value,
      labels: labels
    });
    
    // Maintain max points limit
    if (this.points.length > this.maxPoints) {
      this.points = this.points.slice(-this.maxPoints);
    }
  }

  // Get statistics for recent time period
  public getStats(sinceSeconds: number = 300): Record<string, number> {
    const cutoff = (Date.now() / 1000) - sinceSeconds;
    const recentPoints = this.points.filter(p => p.timestamp >= cutoff).map(p => p.value);
    
    if (recentPoints.length === 0) {
      return {
        count: 0,
        avg: 0,
        min: 0,
        max: 0,
        sum: 0
      };
    }
    
    const sum = recentPoints.reduce((a, b) => a + b, 0);
    const avg = sum / recentPoints.length;
    const min = Math.min(...recentPoints);
    const max = Math.max(...recentPoints);
    const p95 = this.percentile(recentPoints, 0.95);
    const p99 = this.percentile(recentPoints, 0.99);
    
    return {
      count: recentPoints.length,
      avg: avg,
      min: min,
      max: max,
      sum: sum,
      p95: p95,
      p99: p99
    };
  }

  // Calculate percentile
  private percentile(values: number[], p: number): number {
    if (values.length === 0) return 0;
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.floor(sorted.length * p);
    return sorted[Math.min(index, sorted.length - 1)];
  }
}

// Enhanced metrics manager
export class EnhancedMetricsManager {
  public queryTimes: TimeSeriesMetrics;
  public errorCounts: TimeSeriesMetrics;
  public cacheHitRates: TimeSeriesMetrics;
  public systemMetrics: TimeSeriesMetrics;
  
  private alertCallbacks: Array<(alertType: string, context: Record<string, any>) => void> = [];
  private alertRules: Record<string, Record<string, any>>;
  private shutdownEvent: boolean = false;
  private metricsInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.queryTimes = new TimeSeriesMetrics();
    this.errorCounts = new TimeSeriesMetrics();
    this.cacheHitRates = new TimeSeriesMetrics();
    this.systemMetrics = new TimeSeriesMetrics();
    this.alertRules = this.setupDefaultAlertRules();
  }

  // Start monitoring
  public startMonitoring(): void {
    if (!this.metricsInterval) {
      this.metricsInterval = setInterval(() => {
        this.collectSystemMetrics();
      }, 30000); // Collect every 30 seconds
    }
  }

  // Stop monitoring
  public stopMonitoring(): void {
    this.shutdownEvent = true;
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }
  }

  // Record query time
  public recordQueryTime(duration: number, queryType?: string): void {
    const labels = queryType ? { query_type: queryType } : undefined;
    this.queryTimes.addPoint(duration, labels);
    
    // Check for slow query alerts
    if (duration > 2.0) { // Slow query over 2 seconds
      this.triggerAlert("Slow Query", { duration: duration, query_type: queryType });
    }
  }

  // Record error
  public recordError(errorType: string, severity: string = "medium"): void {
    this.errorCounts.addPoint(1, { error_type: errorType, severity: severity });
    
    if (severity === "high") {
      this.triggerAlert("High Severity Error", { error_type: errorType });
    }
  }

  // Record cache hit rate
  public recordCacheHitRate(hitRate: number, cacheType?: string): void {
    const labels = cacheType ? { cache_type: cacheType } : undefined;
    this.cacheHitRates.addPoint(hitRate, labels);
    
    // Check for low hit rate alerts
    if (hitRate < 0.6) { // Hit rate below 60%
      this.triggerAlert("Low Cache Hit Rate", { hit_rate: hitRate, cache_type: cacheType });
    }
  }

  // Collect system metrics
  private collectSystemMetrics(): void {
    // In a real implementation, we would collect actual system metrics
    // For now, we'll just simulate this
    try {
      // This would be where we collect CPU and memory usage
      // Since we don't have access to system metrics in this environment,
      // we'll skip the actual collection
    } catch (e) {
      // Don't let metrics collection affect system operation
    }
  }

  // Setup default alert rules
  private setupDefaultAlertRules(): Record<string, Record<string, any>> {
    return {
      "Slow Query": { threshold: 2.0, window: 300, count: 5 },
      "High Error Rate": { threshold: 0.05, window: 300 },
      "Low Cache Hit Rate": { threshold: 0.6, window: 600 }
    };
  }

  // Add alert callback
  public addAlertCallback(callback: (alertType: string, context: Record<string, any>) => void): void {
    this.alertCallbacks.push(callback);
  }

  // Trigger alert
  private triggerAlert(alertType: string, context: Record<string, any>): void {
    for (const callback of this.alertCallbacks) {
      try {
        callback(alertType, context);
      } catch (e) {
        // Don't let alert failures affect system
      }
    }
  }

  // Get comprehensive metrics
  public getComprehensiveMetrics(): Record<string, any> {
    return {
      query_performance: this.queryTimes.getStats(),
      error_statistics: this.errorCounts.getStats(),
      cache_performance: this.cacheHitRates.getStats(),
      system_metrics: this.systemMetrics.getStats(),
      alert_rules: this.alertRules
    };
  }
}

// Performance metrics class (legacy compatibility)
export class PerformanceMetrics {
  public queryCount: number = 0;
  public totalQueryTime: number = 0.0;
  public slowQueryCount: number = 0;
  public errorCount: number = 0;
  public cacheHits: number = 0;
  public cacheMisses: number = 0;
  public connectionPoolHits: number = 0;
  public connectionPoolWaits: number = 0;

  // Get average query time
  public getAvgQueryTime(): number {
    return this.totalQueryTime / Math.max(this.queryCount, 1);
  }

  // Get cache hit rate
  public getCacheHitRate(): number {
    const total = this.cacheHits + this.cacheMisses;
    return this.cacheHits / Math.max(total, 1);
  }

  // Get error rate
  public getErrorRate(): number {
    return this.errorCount / Math.max(this.queryCount, 1);
  }

  // Convert to object
  public toObject(): Record<string, any> {
    return {
      [StringConstants.FIELD_QUERY_COUNT]: this.queryCount,
      [StringConstants.FIELD_AVG_QUERY_TIME]: this.getAvgQueryTime(),
      [StringConstants.FIELD_SLOW_QUERY_COUNT]: this.slowQueryCount,
      [StringConstants.FIELD_ERROR_COUNT]: this.errorCount,
      [StringConstants.FIELD_ERROR_RATE]: this.getErrorRate(),
      [StringConstants.FIELD_CACHE_HIT_RATE]: this.getCacheHitRate(),
      [StringConstants.FIELD_CONNECTION_POOL_HITS]: this.connectionPoolHits,
      [StringConstants.FIELD_CONNECTION_POOL_WAITS]: this.connectionPoolWaits
    };
  }
}