// Smart Cache Implementation
import { DefaultConfig, StringConstants } from './constants.js';
// Smart cache class
export class SmartCache {
    max_size;
    ttl;
    cache;
    lock;
    hit_count;
    miss_count;
    constructor(maxSize, ttl = DefaultConfig.CACHE_TTL) {
        this.max_size = maxSize;
        this.ttl = ttl;
        this.cache = new Map();
        this.lock = false;
        this.hit_count = 0;
        this.miss_count = 0;
    }
    // Get value from cache
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            this.miss_count++;
            return null;
        }
        if (this.isExpired(entry)) {
            this.cache.delete(key);
            this.miss_count++;
            return null;
        }
        // Update access statistics
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        this.hit_count++;
        // Move to end (most recently used)
        this.cache.delete(key);
        this.cache.set(key, entry);
        return entry.data;
    }
    // Put value in cache
    put(key, value) {
        // If key already exists, update and move to end
        if (this.cache.has(key)) {
            const entry = this.cache.get(key);
            entry.data = value;
            entry.createdAt = Date.now();
            entry.accessCount = 0;
            entry.lastAccessed = Date.now();
            this.cache.delete(key);
            this.cache.set(key, entry);
            return;
        }
        // If cache is full, evict LRU item
        if (this.cache.size >= this.max_size) {
            this.evictLRU();
        }
        // Add new entry
        const entry = {
            data: value,
            createdAt: Date.now(),
            accessCount: 0,
            lastAccessed: Date.now()
        };
        this.cache.set(key, entry);
    }
    // Clear cache
    clear() {
        this.cache.clear();
    }
    // Evict least recently used item
    evictLRU() {
        if (this.cache.size === 0)
            return;
        // Find the least recently used item (oldest lastAccessed)
        let oldestKey = null;
        let oldestTime = Number.MAX_SAFE_INTEGER;
        const entries = Array.from(this.cache.entries());
        for (const [key, entry] of entries) {
            if (entry.lastAccessed < oldestTime) {
                oldestTime = entry.lastAccessed;
                oldestKey = key;
            }
        }
        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }
    // Check if entry is expired
    isExpired(entry) {
        return Date.now() - entry.createdAt > this.ttl * 1000;
    }
    // Get cache statistics
    getStats() {
        const total = this.hit_count + this.miss_count;
        return {
            [StringConstants.FIELD_SIZE]: this.cache.size,
            [StringConstants.FIELD_MAX_SIZE]: this.max_size,
            [StringConstants.FIELD_HIT_COUNT]: this.hit_count,
            [StringConstants.FIELD_MISS_COUNT]: this.miss_count,
            [StringConstants.FIELD_HIT_RATE]: total > 0 ? this.hit_count / total : 0,
            [StringConstants.FIELD_TTL]: this.ttl
        };
    }
}
//# sourceMappingURL=cache.js.map