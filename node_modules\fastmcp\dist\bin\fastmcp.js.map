{"version": 3, "sources": ["../../src/bin/fastmcp.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { execa } from \"execa\";\nimport yargs from \"yargs\";\nimport { hideBin } from \"yargs/helpers\";\n\nawait yargs(hideBin(process.argv))\n  .scriptName(\"fastmcp\")\n  .command(\n    \"dev <file>\",\n    \"Start a development server\",\n    (yargs) => {\n      return yargs.positional(\"file\", {\n        demandOption: true,\n        describe: \"The path to the server file\",\n        type: \"string\",\n      });\n    },\n    async (argv) => {\n      try {\n        await execa({\n          stderr: \"inherit\",\n          stdin: \"inherit\",\n          stdout: \"inherit\",\n        })`npx @wong2/mcp-cli npx tsx ${argv.file}`;\n      } catch (error) {\n        console.error(\n          \"[FastMCP Error] Failed to start development server:\",\n          error instanceof Error ? error.message : String(error),\n        );\n        process.exit(1);\n      }\n    },\n  )\n  .command(\n    \"inspect <file>\",\n    \"Inspect a server file\",\n    (yargs) => {\n      return yargs.positional(\"file\", {\n        demandOption: true,\n        describe: \"The path to the server file\",\n        type: \"string\",\n      });\n    },\n    async (argv) => {\n      try {\n        await execa({\n          stderr: \"inherit\",\n          stdout: \"inherit\",\n        })`npx @modelcontextprotocol/inspector npx tsx ${argv.file}`;\n      } catch (error) {\n        console.error(\n          \"[FastMCP Error] Failed to inspect server:\",\n          error instanceof Error ? error.message : String(error),\n        );\n        process.exit(1);\n      }\n    },\n  )\n  .help()\n  .parseAsync();\n"], "mappings": ";;;AAEA,SAAS,aAAa;AACtB,OAAO,WAAW;AAClB,SAAS,eAAe;AAExB,MAAM,MAAM,QAAQ,QAAQ,IAAI,CAAC,EAC9B,WAAW,SAAS,EACpB;AAAA,EACC;AAAA,EACA;AAAA,EACA,CAACA,WAAU;AACT,WAAOA,OAAM,WAAW,QAAQ;AAAA,MAC9B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAS;AACd,QAAI;AACF,YAAM,MAAM;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC,+BAA+B,KAAK,IAAI;AAAA,IAC3C,SAAS,OAAO;AACd,cAAQ;AAAA,QACN;AAAA,QACA,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,MACvD;AACA,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF;AACF,EACC;AAAA,EACC;AAAA,EACA;AAAA,EACA,CAACA,WAAU;AACT,WAAOA,OAAM,WAAW,QAAQ;AAAA,MAC9B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAS;AACd,QAAI;AACF,YAAM,MAAM;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC,gDAAgD,KAAK,IAAI;AAAA,IAC5D,SAAS,OAAO;AACd,cAAQ;AAAA,QACN;AAAA,QACA,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,MACvD;AACA,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF;AACF,EACC,KAAK,EACL,WAAW;", "names": ["yargs"]}