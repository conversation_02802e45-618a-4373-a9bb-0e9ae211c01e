{"version": 3, "sources": ["../src/startStdioServer.ts", "../src/tapTransport.ts"], "sourcesContent": ["import { Client } from \"@modelcontextprotocol/sdk/client/index.js\";\nimport { SSEClientTransportOptions } from \"@modelcontextprotocol/sdk/client/sse.js\";\nimport { SSEClientTransport } from \"@modelcontextprotocol/sdk/client/sse.js\";\nimport { StreamableHTTPClientTransportOptions } from \"@modelcontextprotocol/sdk/client/streamableHttp.js\";\nimport { StreamableHTTPClientTransport } from \"@modelcontextprotocol/sdk/client/streamableHttp.js\";\nimport { Server } from \"@modelcontextprotocol/sdk/server/index.js\";\nimport { StdioServerTransport } from \"@modelcontextprotocol/sdk/server/stdio.js\";\n\nimport { proxyServer } from \"./proxyServer.js\";\n\nexport enum ServerType {\n  HTTPStream = \"HTTPStream\",\n  SSE = \"SSE\",\n}\n\nexport const startStdioServer = async ({\n  initStdioServer,\n  initStreamClient,\n  serverType,\n  transportOptions = {},\n  url,\n}: {\n  initStdioServer?: () => Promise<Server>;\n  initStreamClient?: () => Promise<Client>;\n  serverType: ServerType;\n  transportOptions?:\n    | SSEClientTransportOptions\n    | StreamableHTTPClientTransportOptions;\n  url: string;\n}): Promise<Server> => {\n  let transport: SSEClientTransport | StreamableHTTPClientTransport;\n  switch (serverType) {\n    case ServerType.SSE:\n      transport = new SSEClientTransport(new URL(url), transportOptions);\n      break;\n    default:\n      transport = new StreamableHTTPClientTransport(\n        new URL(url),\n        transportOptions,\n      );\n  }\n  const streamClient = initStreamClient\n    ? await initStreamClient()\n    : new Client(\n        {\n          name: \"mcp-proxy\",\n          version: \"1.0.0\",\n        },\n        {\n          capabilities: {},\n        },\n      );\n  await streamClient.connect(transport);\n\n  const serverVersion = streamClient.getServerVersion() as {\n    name: string;\n    version: string;\n  };\n\n  const serverCapabilities = streamClient.getServerCapabilities() as {\n    capabilities: Record<string, unknown>;\n  };\n\n  const stdioServer = initStdioServer\n    ? await initStdioServer()\n    : new Server(serverVersion, {\n        capabilities: serverCapabilities,\n      });\n\n  const stdioTransport = new StdioServerTransport();\n\n  await stdioServer.connect(stdioTransport);\n\n  await proxyServer({\n    client: streamClient,\n    server: stdioServer,\n    serverCapabilities,\n  });\n\n  return stdioServer;\n};\n", "import { Transport } from \"@modelcontextprotocol/sdk/shared/transport.js\";\nimport { JSONRPCMessage } from \"@modelcontextprotocol/sdk/types.js\";\n\ntype TransportEvent =\n  | {\n      error: Error;\n      type: \"onerror\";\n    }\n  | {\n      message: JSONRPCMessage;\n      type: \"onmessage\";\n    }\n  | {\n      message: JSONRPCMessage;\n      type: \"send\";\n    }\n  | {\n      type: \"close\";\n    }\n  | {\n      type: \"onclose\";\n    }\n  | {\n      type: \"start\";\n    };\n\nexport const tapTransport = (\n  transport: Transport,\n  eventHandler: (event: TransportEvent) => void,\n): Transport => {\n  const originalClose = transport.close.bind(transport);\n  const originalOnClose = transport.onclose?.bind(transport);\n  const originalOnError = transport.onerror?.bind(transport);\n  const originalOnMessage = transport.onmessage?.bind(transport);\n  const originalSend = transport.send.bind(transport);\n  const originalStart = transport.start.bind(transport);\n\n  transport.close = async () => {\n    eventHandler({\n      type: \"close\",\n    });\n\n    return originalClose?.();\n  };\n\n  transport.onclose = async () => {\n    eventHandler({\n      type: \"onclose\",\n    });\n\n    return originalOnClose?.();\n  };\n\n  transport.onerror = async (error: Error) => {\n    eventHandler({\n      error,\n      type: \"onerror\",\n    });\n\n    return originalOnError?.(error);\n  };\n\n  transport.onmessage = async (message: JSONRPCMessage) => {\n    eventHandler({\n      message,\n      type: \"onmessage\",\n    });\n\n    return originalOnMessage?.(message);\n  };\n\n  transport.send = async (message: JSONRPCMessage) => {\n    eventHandler({\n      message,\n      type: \"send\",\n    });\n\n    return originalSend?.(message);\n  };\n\n  transport.start = async () => {\n    eventHandler({\n      type: \"start\",\n    });\n\n    return originalStart?.();\n  };\n\n  return transport;\n};\n"], "mappings": ";;;;;;;AAAA,SAAS,cAAc;AAEvB,SAAS,0BAA0B;AAEnC,SAAS,qCAAqC;AAC9C,SAAS,cAAc;AACvB,SAAS,4BAA4B;AAI9B,IAAK,aAAL,kBAAKA,gBAAL;AACL,EAAAA,YAAA,gBAAa;AACb,EAAAA,YAAA,SAAM;AAFI,SAAAA;AAAA,GAAA;AAKL,IAAM,mBAAmB,OAAO;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB,CAAC;AAAA,EACpB;AACF,MAQuB;AACrB,MAAI;AACJ,UAAQ,YAAY;AAAA,IAClB,KAAK;AACH,kBAAY,IAAI,mBAAmB,IAAI,IAAI,GAAG,GAAG,gBAAgB;AACjE;AAAA,IACF;AACE,kBAAY,IAAI;AAAA,QACd,IAAI,IAAI,GAAG;AAAA,QACX;AAAA,MACF;AAAA,EACJ;AACA,QAAM,eAAe,mBACjB,MAAM,iBAAiB,IACvB,IAAI;AAAA,IACF;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA;AAAA,MACE,cAAc,CAAC;AAAA,IACjB;AAAA,EACF;AACJ,QAAM,aAAa,QAAQ,SAAS;AAEpC,QAAM,gBAAgB,aAAa,iBAAiB;AAKpD,QAAM,qBAAqB,aAAa,sBAAsB;AAI9D,QAAM,cAAc,kBAChB,MAAM,gBAAgB,IACtB,IAAI,OAAO,eAAe;AAAA,IACxB,cAAc;AAAA,EAChB,CAAC;AAEL,QAAM,iBAAiB,IAAI,qBAAqB;AAEhD,QAAM,YAAY,QAAQ,cAAc;AAExC,QAAM,YAAY;AAAA,IAChB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;ACtDO,IAAM,eAAe,CAC1B,WACA,iBACc;AACd,QAAM,gBAAgB,UAAU,MAAM,KAAK,SAAS;AACpD,QAAM,kBAAkB,UAAU,SAAS,KAAK,SAAS;AACzD,QAAM,kBAAkB,UAAU,SAAS,KAAK,SAAS;AACzD,QAAM,oBAAoB,UAAU,WAAW,KAAK,SAAS;AAC7D,QAAM,eAAe,UAAU,KAAK,KAAK,SAAS;AAClD,QAAM,gBAAgB,UAAU,MAAM,KAAK,SAAS;AAEpD,YAAU,QAAQ,YAAY;AAC5B,iBAAa;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAED,WAAO,gBAAgB;AAAA,EACzB;AAEA,YAAU,UAAU,YAAY;AAC9B,iBAAa;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAED,WAAO,kBAAkB;AAAA,EAC3B;AAEA,YAAU,UAAU,OAAO,UAAiB;AAC1C,iBAAa;AAAA,MACX;AAAA,MACA,MAAM;AAAA,IACR,CAAC;AAED,WAAO,kBAAkB,KAAK;AAAA,EAChC;AAEA,YAAU,YAAY,OAAO,YAA4B;AACvD,iBAAa;AAAA,MACX;AAAA,MACA,MAAM;AAAA,IACR,CAAC;AAED,WAAO,oBAAoB,OAAO;AAAA,EACpC;AAEA,YAAU,OAAO,OAAO,YAA4B;AAClD,iBAAa;AAAA,MACX;AAAA,MACA,MAAM;AAAA,IACR,CAAC;AAED,WAAO,eAAe,OAAO;AAAA,EAC/B;AAEA,YAAU,QAAQ,YAAY;AAC5B,iBAAa;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAED,WAAO,gBAAgB;AAAA,EACzB;AAEA,SAAO;AACT;", "names": ["ServerType"]}