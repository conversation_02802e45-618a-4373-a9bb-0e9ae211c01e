{"name": "fastmcp", "version": "2.2.4", "main": "dist/FastMCP.js", "scripts": {"build": "tsup", "lint": "prettier --check . && eslint . && tsc --noEmit && jsr publish --dry-run", "test": "vitest run", "format": "prettier --write . && eslint --fix ."}, "bin": {"fastmcp": "dist/bin/fastmcp.js"}, "keywords": ["MCP", "SSE"], "type": "module", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "description": "A TypeScript framework for building MCP servers.", "module": "dist/FastMCP.js", "types": "dist/FastMCP.d.ts", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "@standard-schema/spec": "^1.0.0", "execa": "^9.6.0", "file-type": "^21.0.0", "fuse.js": "^7.1.0", "mcp-proxy": "^3.0.3", "strict-event-emitter-types": "^2.0.0", "undici": "^7.10.0", "uri-templates": "^0.2.0", "xsschema": "0.3.0-beta.3", "yargs": "^18.0.0", "zod": "^3.25.56", "zod-to-json-schema": "^3.24.5"}, "repository": {"url": "https://github.com/punkpeye/fastmcp"}, "homepage": "https://glama.ai/mcp", "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@sebbo2002/semantic-release-jsr"]}, "devDependencies": {"@eslint/js": "^9.28.0", "@modelcontextprotocol/inspector": "^0.14.0", "@sebbo2002/semantic-release-jsr": "^3.0.0", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.30", "@types/uri-templates": "^0.1.34", "@types/yargs": "^17.0.33", "@valibot/to-json-schema": "^1.3.0", "@wong2/mcp-cli": "^1.10.0", "arktype": "^2.1.20", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-perfectionist": "^4.14.0", "eslint-plugin-prettier": "^5.4.1", "eventsource-client": "^1.1.3", "get-port-please": "^3.1.2", "jiti": "^2.4.2", "jsr": "^0.13.4", "prettier": "^3.5.3", "semantic-release": "^24.2.5", "tsup": "^8.5.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "valibot": "^1.1.0", "vitest": "^3.2.2"}, "tsup": {"entry": ["src/FastMCP.ts", "src/bin/fastmcp.ts"], "format": ["esm"], "dts": true, "splitting": true, "sourcemap": true, "clean": true}}