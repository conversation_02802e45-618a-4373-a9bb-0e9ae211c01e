export declare class MySQLManager {
    private sessionId;
    private configManager;
    private connectionPool;
    private schemaCache;
    private tableExistsCache;
    private indexCache;
    private metrics;
    private enhancedMetrics;
    private securityValidator;
    private adaptiveRateLimiter;
    private retryStrategy;
    private static DANGEROUS_PATTERNS;
    private static TABLE_NAME_PATTERN;
    constructor();
    private executeWithRetry;
    private validateInput;
    private validateQuery;
    private validateTableName;
    private checkRateLimit;
    private getTableSchemaCached;
    private tableExistsCached;
    executeQuery(query: string, params?: any[]): Promise<any>;
    private executeQueryInternal;
    private updateMetrics;
    invalidateCaches(operationType?: string, tableName?: string): void;
    private clearAllCaches;
    private invalidateTableSpecificCache;
    getPerformanceMetrics(): Record<string, any>;
    close(): Promise<void>;
}
//# sourceMappingURL=mysqlManager.d.ts.map