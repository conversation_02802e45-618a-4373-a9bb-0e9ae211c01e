/**
 * MySQL 管理器 - 主集成类
 *
 * 综合的 MySQL 管理系统，集成了连接池、缓存、安全验证、
 * 速率限制和性能监控功能。为所有数据库操作提供统一接口，
 * 具备企业级的可靠性和安全特性。
 *
 * @fileoverview 集成组件的核心 MySQL 管理类
 * <AUTHOR> MCP 团队
 * @since 1.0.0
 */

import { randomUUID } from 'crypto';
import { ConnectionPool } from './connection.js';
import { ConfigurationManager, DatabaseConfig, SecurityConfig, CacheConfig } from './config.js';
import { SmartCache } from './cache.js';
import { EnhancedSecurityValidator } from './security.js';
import { AdaptiveRateLimiter } from './rateLimit.js';
import { EnhancedMetricsManager, PerformanceMetrics } from './metrics.js';
import { StringConstants, DefaultConfig, MySQLErrorCodes } from './constants.js';
import { PoolConnection } from 'mysql2/promise';

/**
 * 重试策略接口
 *
 * 定义实现重试机制的契约，具有可配置的退避策略
 * 用于处理瞬态数据库错误。
 */
interface RetryStrategy {
  /** 放弃前的最大重试次数 */
  maxAttempts: number;
  /** 第一次重试的基础延迟（秒） */
  baseDelay: number;
  /** 防止过度等待的最大延迟（秒） */
  maxDelay: number;
  /** 每次重试尝试的指数退避乘数 */
  backoffFactor: number;

  /**
   * 计算特定重试尝试的延迟
   * @param attempt - 当前尝试次数（从0开始）
   * @returns 下次重试前的延迟秒数
   */
  getDelay(attempt: number): number;
}

/**
 * 默认重试策略实现
 *
 * 实现具有可配置参数的指数退避算法。
 * 为大多数数据库重试场景提供合理的默认值。
 */
class DefaultRetryStrategy implements RetryStrategy {
  maxAttempts: number = DefaultConfig.MAX_RETRY_ATTEMPTS;
  baseDelay: number = 1.0;
  maxDelay: number = 10.0;
  backoffFactor: number = 2.0;

  /**
   * 计算带有最大上限的指数退避延迟
   * @param attempt - 当前重试尝试次数
   * @returns 计算出的延迟秒数
   */
  getDelay(attempt: number): number {
    const delay = this.baseDelay * Math.pow(this.backoffFactor, attempt);
    return Math.min(delay, this.maxDelay);
  }
}

/**
 * MySQL 管理器类
 *
 * 中央管理类，协调所有 MySQL 操作，集成了安全性、
 * 性能监控、缓存和连接管理功能。
 *
 * 功能特性：
 * - 带健康监控的连接池
 * - 多级缓存（模式、表存在性、索引）
 * - 安全验证和 SQL 注入防护
 * - 基于系统负载的自适应速率限制
 * - 全面的性能指标和告警
 * - 指数退避的自动重试
 * - 优雅的错误处理和恢复
 *
 * @class MySQLManager
 * @since 1.0.0
 */
export class MySQLManager {
  /** 用于跟踪和调试的唯一会话标识符 */
  private sessionId: string;

  /** 数据库、安全和缓存设置的配置管理器 */
  private configManager: ConfigurationManager;

  /** 高效数据库连接的连接池管理器 */
  private connectionPool: ConnectionPool;

  /** 表模式信息的智能缓存 */
  private schemaCache: SmartCache<any>;

  /** 表存在性检查的智能缓存 */
  private tableExistsCache: SmartCache<boolean>;

  /** 索引信息的智能缓存 */
  private indexCache: SmartCache<any>;

  /** 传统性能指标收集器 */
  private metrics: PerformanceMetrics;

  /** 带时间序列数据和告警的增强指标管理器 */
  private enhancedMetrics: EnhancedMetricsManager;

  /** 用于输入清理和 SQL 注入防护的安全验证器 */
  private securityValidator: EnhancedSecurityValidator;

  /** 请求节流的自适应速率限制器 */
  private adaptiveRateLimiter: AdaptiveRateLimiter;

  /** 处理瞬态错误的重试策略 */
  private retryStrategy: RetryStrategy;

  /**
   * Pre-compiled dangerous SQL patterns for security validation
   * These patterns detect potentially harmful SQL operations that could
   * compromise system security or data integrity.
   */
  private static DANGEROUS_PATTERNS: RegExp[] = [
    /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
    /\b(SYSTEM|EXEC|SHELL)\b/i,
    /\bINTO\s+OUTFILE\b/i,
    /\bLOAD\s+DATA\b/i,
  ];

  /**
   * Table name validation pattern
   * Ensures table names contain only safe characters (alphanumeric, underscore, hyphen)
   */
  private static TABLE_NAME_PATTERN: RegExp = /^[a-zA-Z0-9_-]+$/;
  
  /**
   * MySQL Manager Constructor
   *
   * Initializes all components of the MySQL management system including
   * configuration, connection pooling, caching, security, and monitoring.
   *
   * @constructor
   * @throws {Error} When component initialization fails
   */
  constructor() {
    // Generate unique session identifier for tracking
    this.sessionId = randomUUID();

    // Initialize centralized configuration management
    this.configManager = new ConfigurationManager();

    // Initialize connection pool with database configuration
    this.connectionPool = new ConnectionPool(this.configManager.database);

    // Initialize smart caching system with configured sizes and TTL
    this.schemaCache = new SmartCache(
      this.configManager.cache.schemaCacheSize,
      this.configManager.cache.cacheTTL
    );
    this.tableExistsCache = new SmartCache(
      this.configManager.cache.tableExistsCacheSize,
      this.configManager.cache.cacheTTL
    );
    this.indexCache = new SmartCache(
      this.configManager.cache.indexCacheSize,
      this.configManager.cache.cacheTTL
    );

    // Initialize performance monitoring systems
    this.metrics = new PerformanceMetrics();
    this.enhancedMetrics = new EnhancedMetricsManager();
    this.enhancedMetrics.startMonitoring();

    // Initialize security validation system
    this.securityValidator = new EnhancedSecurityValidator();

    // Initialize adaptive rate limiting with security configuration
    this.adaptiveRateLimiter = new AdaptiveRateLimiter(
      this.configManager.security.rateLimitMax,
      this.configManager.security.rateLimitWindow
    );

    // Initialize retry strategy for error handling
    this.retryStrategy = new DefaultRetryStrategy();
  }
  
  /**
   * Execute Operation with Retry Mechanism
   *
   * Executes database operations with automatic retry using exponential backoff.
   * Handles transient errors while avoiding retries for permanent failures.
   *
   * @private
   * @template T - Return type of the operation
   * @param operation - Async operation to execute with retry logic
   * @returns Promise resolving to operation result
   * @throws {Error} When all retry attempts are exhausted or non-retryable error occurs
   */
  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.retryStrategy.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;

        // Skip retry for certain error types that won't resolve with retries
        if (error.code) {
          const errorCode = parseInt(error.code, 10);
          if (errorCode === MySQLErrorCodes.ACCESS_DENIED ||
              errorCode === MySQLErrorCodes.PARSE_ERROR) {
            break;
          }
        }

        // Apply exponential backoff delay before next attempt
        if (attempt < this.retryStrategy.maxAttempts - 1) {
          const delay = this.retryStrategy.getDelay(attempt) * 1000; // Convert to milliseconds
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error("Operation failed after all retries");
  }

  /**
   * Validate Input Data
   *
   * Performs comprehensive security validation on input data to prevent
   * SQL injection and other security vulnerabilities.
   *
   * @private
   * @param inputValue - Value to validate (string, number, boolean, null, undefined)
   * @param fieldName - Name of the field being validated (for error messages)
   * @param validationLevel - Validation strictness level ("strict", "moderate", "basic")
   * @throws {Error} When input fails security validation
   */
  private validateInput(inputValue: any, fieldName: string, validationLevel: string = "strict"): void {
    EnhancedSecurityValidator.validateInputComprehensive(inputValue, fieldName, validationLevel);
  }
  
  /**
   * Validate SQL Query Security
   *
   * Performs comprehensive security validation on SQL queries including
   * length limits, dangerous pattern detection, and query type restrictions.
   *
   * @private
   * @param query - SQL query string to validate
   * @throws {Error} When query fails security validation
   */
  private validateQuery(query: string): void {
    // Check query length against configured maximum
    if (query.length > this.configManager.security.maxQueryLength) {
      throw new Error(StringConstants.MSG_QUERY_TOO_LONG);
    }

    // Scan for dangerous SQL patterns that could compromise security
    for (const pattern of MySQLManager.DANGEROUS_PATTERNS) {
      if (pattern.test(query)) {
        throw new Error(StringConstants.MSG_PROHIBITED_OPERATIONS);
      }
    }

    // Extract and validate query type (first word)
    const firstWordEnd = query.indexOf(' ');
    const queryType = (firstWordEnd !== -1 ? query.substring(0, firstWordEnd) : query).trim().toUpperCase();

    // Ensure query type is in the allowed list
    if (!this.configManager.security.allowedQueryTypes.includes(queryType)) {
      throw new Error(StringConstants.MSG_QUERY_TYPE_NOT_ALLOWED.replace('{query_type}', queryType));
    }
  }

  /**
   * Validate Table Name
   *
   * Validates table names against security patterns and length restrictions
   * to prevent SQL injection and ensure compatibility.
   *
   * @private
   * @param tableName - Table name to validate
   * @throws {Error} When table name is invalid or too long
   */
  private validateTableName(tableName: string): void {
    // Check against allowed character pattern
    if (!MySQLManager.TABLE_NAME_PATTERN.test(tableName)) {
      throw new Error(StringConstants.MSG_INVALID_TABLE_NAME);
    }

    // Check length limit
    if (tableName.length > DefaultConfig.MAX_TABLE_NAME_LENGTH) {
      throw new Error(StringConstants.MSG_TABLE_NAME_TOO_LONG);
    }
  }

  /**
   * Check Rate Limit
   *
   * Verifies that the current request is within rate limiting bounds
   * using the adaptive rate limiter.
   *
   * @private
   * @param identifier - Unique identifier for rate limiting (defaults to "default")
   * @throws {Error} When rate limit is exceeded
   */
  private checkRateLimit(identifier: string = "default"): void {
    if (!this.adaptiveRateLimiter.checkRateLimit(identifier)) {
      throw new Error(StringConstants.MSG_RATE_LIMIT_EXCEEDED);
    }
  }
  
  /**
   * Get Table Schema with Caching
   *
   * Retrieves table schema information with intelligent caching to improve
   * performance. Cache misses trigger database queries while hits return
   * cached data immediately.
   *
   * @private
   * @param tableName - Name of the table to get schema for
   * @returns Promise resolving to table schema information
   * @throws {Error} When schema query fails
   */
  private async getTableSchemaCached(tableName: string): Promise<any> {
    const cacheKey = `schema_${tableName}`;
    let result = this.schemaCache.get(cacheKey);

    if (result === null) {
      // Cache miss: execute schema query
      const schemaQuery = `
        SELECT
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `;

      result = await this.executeQuery(schemaQuery, [tableName]);
      this.schemaCache.put(cacheKey, result);
      this.metrics.cacheMisses++;
    } else {
      // Cache hit: return cached data
      this.metrics.cacheHits++;
    }

    return result ?? false;
  }

  /**
   * Check Table Existence with Caching
   *
   * Verifies if a table exists in the current database with caching
   * to avoid repeated INFORMATION_SCHEMA queries.
   *
   * @private
   * @param tableName - Name of the table to check
   * @returns Promise resolving to true if table exists, false otherwise
   * @throws {Error} When existence check query fails
   */
  private async tableExistsCached(tableName: string): Promise<boolean> {
    const cacheKey = `exists_${tableName}`;
    let result = this.tableExistsCache.get(cacheKey);

    if (result === null) {
      // Cache miss: execute existence check query
      const existsQuery = `
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
      `;

      const queryResult = await this.executeQuery(existsQuery, [tableName]);
      result = queryResult && queryResult[0] && queryResult[0].count > 0;
      this.tableExistsCache.put(cacheKey, result ?? false);
      this.metrics.cacheMisses++;
    } else {
      // Cache hit: return cached result
      this.metrics.cacheHits++;
    }

    return result ?? false;
  }
  
  /**
   * Execute SQL Query
   *
   * Main public method for executing SQL queries with comprehensive security,
   * performance monitoring, caching, and error handling. Includes rate limiting,
   * retry mechanisms, and metrics collection.
   *
   * @public
   * @param query - SQL query string to execute
   * @param params - Optional parameters for prepared statements
   * @returns Promise resolving to query results
   * @throws {Error} When rate limit exceeded, security validation fails, or query execution fails
   *
   * @example
   * // Simple query
   * const results = await manager.executeQuery("SELECT * FROM users LIMIT 10");
   *
   * @example
   * // Parameterized query
   * const user = await manager.executeQuery("SELECT * FROM users WHERE id = ?", [123]);
   */
  public async executeQuery(query: string, params?: any[]): Promise<any> {
    const startTime = Date.now();

    try {
      // Apply rate limiting to prevent abuse
      this.checkRateLimit();

      // Validate query for security compliance
      this.validateQuery(query);

      // Execute with automatic retry on transient failures
      const result = await this.executeWithRetry(async () => {
        return await this.executeQueryInternal(query, params);
      });

      // Record successful execution metrics
      const queryTime = (Date.now() - startTime) / 1000; // Convert to seconds
      const isSlow = queryTime > DefaultConfig.SLOW_QUERY_THRESHOLD;
      this.updateMetrics(queryTime, false, isSlow);

      return result;
    } catch (error) {
      // Record error metrics
      const queryTime = (Date.now() - startTime) / 1000; // Convert to seconds
      this.updateMetrics(queryTime, true, false);
      throw error;
    }
  }

  /**
   * Internal Query Execution
   *
   * Low-level method that handles the actual database connection and query execution.
   * Manages connection lifecycle and ensures proper resource cleanup.
   *
   * @private
   * @param query - SQL query string
   * @param params - Optional query parameters
   * @returns Promise resolving to raw query results
   * @throws {Error} When connection or query execution fails
   */
  private async executeQueryInternal(query: string, params?: any[]): Promise<any> {
    const connection = await this.connectionPool.getConnection();

    try {
      const [rows] = await connection.execute(query, params);
      return rows;
    } finally {
      // Always release connection back to pool
      connection.release();
    }
  }
  
  // Update performance metrics
  private updateMetrics(queryTime: number, isError: boolean = false, isSlow: boolean = false): void {
    this.metrics.queryCount++;
    this.metrics.totalQueryTime += queryTime;
    
    if (isError) {
      this.metrics.errorCount++;
      this.enhancedMetrics.recordError("query_error", "medium");
    }
    
    if (isSlow) {
      this.metrics.slowQueryCount++;
    }
    
    // Record to enhanced metrics manager
    this.enhancedMetrics.recordQueryTime(queryTime);
    
    // Update cache hit rate metrics
    const cacheHitRate = this.metrics.getCacheHitRate();
    this.enhancedMetrics.recordCacheHitRate(cacheHitRate);
  }
  
  // Invalidate caches
  public invalidateCaches(operationType: string = "DDL", tableName?: string): void {
    if (operationType === "DDL" || operationType === "CREATE" || operationType === "DROP" || operationType === "ALTER") {
      // DDL operations clear all caches
      this.clearAllCaches();
    } else if (operationType === "DML" && tableName) {
      // DML operations clear specific table cache
      this.invalidateTableSpecificCache(tableName);
    }
  }
  
  // Clear all caches
  private clearAllCaches(): void {
    this.schemaCache.clear();
    this.tableExistsCache.clear();
    this.indexCache.clear();
  }
  
  // Invalidate specific table cache
  private invalidateTableSpecificCache(tableName: string): void {
    const cacheKeysToRemove = [
      `schema_${tableName}`,
      `exists_${tableName}`,
      `indexes_${tableName}`
    ];
    
    // Remove keys from all caches
    cacheKeysToRemove.forEach(key => {
      // We can't directly access the cache internals, so we'll just clear them
      // In a real implementation, we would have a method to remove specific keys
    });
  }
  
  /**
   * Get Performance Metrics
   *
   * Retrieves comprehensive performance metrics including query statistics,
   * cache performance, and connection pool status for monitoring and debugging.
   *
   * @public
   * @returns Object containing detailed performance metrics
   *
   * @example
   * const metrics = manager.getPerformanceMetrics();
   * console.log(`Cache hit rate: ${metrics.cache_stats.schema_cache.hit_rate}`);
   */
  public getPerformanceMetrics(): Record<string, any> {
    return {
      [StringConstants.SECTION_PERFORMANCE]: this.metrics.toObject(),
      [StringConstants.SECTION_CACHE_STATS]: {
        [StringConstants.SECTION_SCHEMA_CACHE]: this.schemaCache.getStats(),
        [StringConstants.SECTION_TABLE_EXISTS_CACHE]: this.tableExistsCache.getStats(),
        [StringConstants.SECTION_INDEX_CACHE]: this.indexCache.getStats()
      },
      [StringConstants.SECTION_CONNECTION_POOL]: this.connectionPool.getStats()
    };
  }

  /**
   * Close MySQL Manager
   *
   * Performs graceful shutdown of all components including metrics monitoring,
   * connection pool closure, and cache cleanup. Should be called during
   * application shutdown to prevent resource leaks.
   *
   * @public
   * @returns Promise that resolves when all cleanup is complete
   *
   * @example
   * // Graceful shutdown
   * await manager.close();
   */
  public async close(): Promise<void> {
    try {
      // Stop enhanced metrics monitoring
      this.enhancedMetrics.stopMonitoring();

      // Close connection pool and release all connections
      await this.connectionPool.close();

      // Clear all caches to free memory
      this.schemaCache.clear();
      this.tableExistsCache.clear();
      this.indexCache.clear();
    } catch (error) {
      console.error(`${StringConstants.MSG_ERROR_DURING_CLEANUP} ${error}`);
    }
  }
}