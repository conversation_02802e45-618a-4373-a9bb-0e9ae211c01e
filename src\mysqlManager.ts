// MySQL管理器 - 集成所有组件的主类
import { randomUUID } from 'crypto';
import { ConnectionPool } from './connection.js';
import { ConfigurationManager, DatabaseConfig, SecurityConfig, CacheConfig } from './config.js';
import { SmartCache } from './cache.js';
import { EnhancedSecurityValidator } from './security.js';
import { AdaptiveRateLimiter } from './rateLimit.js';
import { EnhancedMetricsManager, PerformanceMetrics } from './metrics.js';
import { StringConstants, DefaultConfig, MySQLErrorCodes } from './constants.js';
import { PoolConnection } from 'mysql2/promise';

// 重试策略接口
interface RetryStrategy {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  getDelay(attempt: number): number;
}

// 默认重试策略
class DefaultRetryStrategy implements RetryStrategy {
  maxAttempts: number = DefaultConfig.MAX_RETRY_ATTEMPTS;
  baseDelay: number = 1.0;
  maxDelay: number = 10.0;
  backoffFactor: number = 2.0;
  
  getDelay(attempt: number): number {
    const delay = this.baseDelay * Math.pow(this.backoffFactor, attempt);
    return Math.min(delay, this.maxDelay);
  }
}

export class MySQLManager {
  private sessionId: string;
  private configManager: ConfigurationManager;
  private connectionPool: ConnectionPool;
  private schemaCache: SmartCache<any>;
  private tableExistsCache: SmartCache<boolean>;
  private indexCache: SmartCache<any>;
  private metrics: PerformanceMetrics;
  private enhancedMetrics: EnhancedMetricsManager;
  private securityValidator: EnhancedSecurityValidator;
  private adaptiveRateLimiter: AdaptiveRateLimiter;
  private retryStrategy: RetryStrategy;
  
  // 预编译的正则表达式模式
  private static DANGEROUS_PATTERNS: RegExp[] = [
    /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
    /\b(SYSTEM|EXEC|SHELL)\b/i,
    /\bINTO\s+OUTFILE\b/i,
    /\bLOAD\s+DATA\b/i,
  ];
  private static TABLE_NAME_PATTERN: RegExp = /^[a-zA-Z0-9_-]+$/;
  
  constructor() {
    this.sessionId = randomUUID();
    
    // 集中配置管理
    this.configManager = new ConfigurationManager();
    
    // 连接池
    this.connectionPool = new ConnectionPool(this.configManager.database);
    
    // 智能缓存
    this.schemaCache = new SmartCache(
      this.configManager.cache.schemaCacheSize,
      this.configManager.cache.cacheTTL
    );
    this.tableExistsCache = new SmartCache(
      this.configManager.cache.tableExistsCacheSize,
      this.configManager.cache.cacheTTL
    );
    this.indexCache = new SmartCache(
      this.configManager.cache.indexCacheSize,
      this.configManager.cache.cacheTTL
    );
    
    // 性能指标
    this.metrics = new PerformanceMetrics();
    this.enhancedMetrics = new EnhancedMetricsManager();
    this.enhancedMetrics.startMonitoring();
    
    // 安全验证器
    this.securityValidator = new EnhancedSecurityValidator();
    
    // 自适应频率限制器
    this.adaptiveRateLimiter = new AdaptiveRateLimiter(
      this.configManager.security.rateLimitMax,
      this.configManager.security.rateLimitWindow
    );
    
    // 重试策略
    this.retryStrategy = new DefaultRetryStrategy();
  }
  
  // 带重试机制的查询执行
  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.retryStrategy.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;
        
        // 某些错误不应该重试
        if (error.code) {
          const errorCode = parseInt(error.code, 10);
          if (errorCode === MySQLErrorCodes.ACCESS_DENIED || 
              errorCode === MySQLErrorCodes.PARSE_ERROR) {
            break;
          }
        }
        
        if (attempt < this.retryStrategy.maxAttempts - 1) {
          const delay = this.retryStrategy.getDelay(attempt) * 1000; // 转换为毫秒
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError || new Error("Operation failed after all retries");
  }
  
  // 验证输入
  private validateInput(inputValue: any, fieldName: string, validationLevel: string = "strict"): void {
    EnhancedSecurityValidator.validateInputComprehensive(inputValue, fieldName, validationLevel);
  }
  
  // 验证SQL查询
  private validateQuery(query: string): void {
    if (query.length > this.configManager.security.maxQueryLength) {
      throw new Error(StringConstants.MSG_QUERY_TOO_LONG);
    }
    
    // 检查危险模式
    for (const pattern of MySQLManager.DANGEROUS_PATTERNS) {
      if (pattern.test(query)) {
        throw new Error(StringConstants.MSG_PROHIBITED_OPERATIONS);
      }
    }
    
    // 获取查询类型（第一个单词）
    const firstWordEnd = query.indexOf(' ');
    const queryType = (firstWordEnd !== -1 ? query.substring(0, firstWordEnd) : query).trim().toUpperCase();
    
    if (!this.configManager.security.allowedQueryTypes.includes(queryType)) {
      throw new Error(StringConstants.MSG_QUERY_TYPE_NOT_ALLOWED.replace('{query_type}', queryType));
    }
  }
  
  // 验证表名
  private validateTableName(tableName: string): void {
    if (!MySQLManager.TABLE_NAME_PATTERN.test(tableName)) {
      throw new Error(StringConstants.MSG_INVALID_TABLE_NAME);
    }
    
    if (tableName.length > DefaultConfig.MAX_TABLE_NAME_LENGTH) {
      throw new Error(StringConstants.MSG_TABLE_NAME_TOO_LONG);
    }
  }
  
  // 检查频率限制
  private checkRateLimit(identifier: string = "default"): void {
    if (!this.adaptiveRateLimiter.checkRateLimit(identifier)) {
      throw new Error(StringConstants.MSG_RATE_LIMIT_EXCEEDED);
    }
  }
  
  // 获取表结构（带缓存）
  private async getTableSchemaCached(tableName: string): Promise<any> {
    const cacheKey = `schema_${tableName}`;
    let result = this.schemaCache.get(cacheKey);
    
    if (result === null) {
      // 表结构查询
      const schemaQuery = `
        SELECT 
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `;
      
      result = await this.executeQuery(schemaQuery, [tableName]);
      this.schemaCache.put(cacheKey, result);
      this.metrics.cacheMisses++;
    } else {
      this.metrics.cacheHits++;
    }
    
    return result ?? false;
  }
  
  // 检查表是否存在（带缓存）
  private async tableExistsCached(tableName: string): Promise<boolean> {
    const cacheKey = `exists_${tableName}`;
    let result = this.tableExistsCache.get(cacheKey);
    
    if (result === null) {
      // 表存在性检查查询
      const existsQuery = `
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
      `;
      
      const queryResult = await this.executeQuery(existsQuery, [tableName]);
      result = queryResult && queryResult[0] && queryResult[0].count > 0;
      this.tableExistsCache.put(cacheKey, result ?? false);
      this.metrics.cacheMisses++;
    } else {
      this.metrics.cacheHits++;
    }
    
    return result ?? false;
  }
  
  // 执行SQL查询
  public async executeQuery(query: string, params?: any[]): Promise<any> {
    const startTime = Date.now();
    
    try {
      // 检查频率限制
      this.checkRateLimit();
      
      // 验证查询安全性
      this.validateQuery(query);
      
      // 使用重试机制执行
      const result = await this.executeWithRetry(async () => {
        return await this.executeQueryInternal(query, params);
      });
      
      // Update performance metrics
      const queryTime = (Date.now() - startTime) / 1000; // Convert to seconds
      const isSlow = queryTime > DefaultConfig.SLOW_QUERY_THRESHOLD;
      this.updateMetrics(queryTime, false, isSlow);
      
      return result;
    } catch (error) {
      const queryTime = (Date.now() - startTime) / 1000; // Convert to seconds
      this.updateMetrics(queryTime, true, false);
      throw error;
    }
  }
  
  // Internal query execution
  private async executeQueryInternal(query: string, params?: any[]): Promise<any> {
    const connection = await this.connectionPool.getConnection();
    
    try {
      const [rows, fields] = await connection.execute(query, params);
      return rows;
    } finally {
      connection.release();
    }
  }
  
  // Update performance metrics
  private updateMetrics(queryTime: number, isError: boolean = false, isSlow: boolean = false): void {
    this.metrics.queryCount++;
    this.metrics.totalQueryTime += queryTime;
    
    if (isError) {
      this.metrics.errorCount++;
      this.enhancedMetrics.recordError("query_error", "medium");
    }
    
    if (isSlow) {
      this.metrics.slowQueryCount++;
    }
    
    // Record to enhanced metrics manager
    this.enhancedMetrics.recordQueryTime(queryTime);
    
    // Update cache hit rate metrics
    const cacheHitRate = this.metrics.getCacheHitRate();
    this.enhancedMetrics.recordCacheHitRate(cacheHitRate);
  }
  
  // Invalidate caches
  public invalidateCaches(operationType: string = "DDL", tableName?: string): void {
    if (operationType === "DDL" || operationType === "CREATE" || operationType === "DROP" || operationType === "ALTER") {
      // DDL operations clear all caches
      this.clearAllCaches();
    } else if (operationType === "DML" && tableName) {
      // DML operations clear specific table cache
      this.invalidateTableSpecificCache(tableName);
    }
  }
  
  // Clear all caches
  private clearAllCaches(): void {
    this.schemaCache.clear();
    this.tableExistsCache.clear();
    this.indexCache.clear();
  }
  
  // Invalidate specific table cache
  private invalidateTableSpecificCache(tableName: string): void {
    const cacheKeysToRemove = [
      `schema_${tableName}`,
      `exists_${tableName}`,
      `indexes_${tableName}`
    ];
    
    // Remove keys from all caches
    cacheKeysToRemove.forEach(key => {
      // We can't directly access the cache internals, so we'll just clear them
      // In a real implementation, we would have a method to remove specific keys
    });
  }
  
  // Get performance metrics
  public getPerformanceMetrics(): Record<string, any> {
    return {
      [StringConstants.SECTION_PERFORMANCE]: this.metrics.toObject(),
      [StringConstants.SECTION_CACHE_STATS]: {
        [StringConstants.SECTION_SCHEMA_CACHE]: this.schemaCache.getStats(),
        [StringConstants.SECTION_TABLE_EXISTS_CACHE]: this.tableExistsCache.getStats(),
        [StringConstants.SECTION_INDEX_CACHE]: this.indexCache.getStats()
      },
      [StringConstants.SECTION_CONNECTION_POOL]: this.connectionPool.getStats()
    };
  }
  
  // Close manager
  public async close(): Promise<void> {
    try {
      // Stop enhanced metrics monitoring
      this.enhancedMetrics.stopMonitoring();
      
      // Close connection pool
      await this.connectionPool.close();
      
      // Clear caches
      this.schemaCache.clear();
      this.tableExistsCache.clear();
      this.indexCache.clear();
    } catch (error) {
      console.error(`${StringConstants.MSG_ERROR_DURING_CLEANUP} ${error}`);
    }
  }
}