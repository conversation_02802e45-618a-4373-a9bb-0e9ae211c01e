/**
 * MySQL Manager - Main Integration Class
 *
 * Comprehensive MySQL management system that integrates connection pooling,
 * caching, security validation, rate limiting, and performance monitoring.
 * Provides a unified interface for all database operations with enterprise-grade
 * reliability and security features.
 *
 * @fileoverview Core MySQL management class with integrated components
 * <AUTHOR> MCP Team
 * @since 1.0.0
 */

import { randomUUID } from 'crypto';
import { ConnectionPool } from './connection.js';
import { ConfigurationManager, DatabaseConfig, SecurityConfig, CacheConfig } from './config.js';
import { SmartCache } from './cache.js';
import { EnhancedSecurityValidator } from './security.js';
import { AdaptiveRateLimiter } from './rateLimit.js';
import { EnhancedMetricsManager, PerformanceMetrics } from './metrics.js';
import { StringConstants, DefaultConfig, MySQLErrorCodes } from './constants.js';
import { PoolConnection } from 'mysql2/promise';

/**
 * Retry Strategy Interface
 *
 * Defines the contract for implementing retry mechanisms with configurable
 * backoff strategies for handling transient database errors.
 */
interface RetryStrategy {
  /** Maximum number of retry attempts before giving up */
  maxAttempts: number;
  /** Base delay in seconds for the first retry */
  baseDelay: number;
  /** Maximum delay in seconds to prevent excessive waiting */
  maxDelay: number;
  /** Exponential backoff multiplier for each retry attempt */
  backoffFactor: number;

  /**
   * Calculate delay for a specific retry attempt
   * @param attempt - The current attempt number (0-based)
   * @returns Delay in seconds before the next retry
   */
  getDelay(attempt: number): number;
}

/**
 * Default Retry Strategy Implementation
 *
 * Implements exponential backoff with configurable parameters.
 * Provides reasonable defaults for most database retry scenarios.
 */
class DefaultRetryStrategy implements RetryStrategy {
  maxAttempts: number = DefaultConfig.MAX_RETRY_ATTEMPTS;
  baseDelay: number = 1.0;
  maxDelay: number = 10.0;
  backoffFactor: number = 2.0;

  /**
   * Calculate exponential backoff delay with maximum cap
   * @param attempt - Current retry attempt number
   * @returns Calculated delay in seconds
   */
  getDelay(attempt: number): number {
    const delay = this.baseDelay * Math.pow(this.backoffFactor, attempt);
    return Math.min(delay, this.maxDelay);
  }
}

/**
 * MySQL Manager Class
 *
 * Central management class that orchestrates all MySQL operations with integrated
 * security, performance monitoring, caching, and connection management.
 *
 * Features:
 * - Connection pooling with health monitoring
 * - Multi-level caching (schema, table existence, indexes)
 * - Security validation and SQL injection prevention
 * - Adaptive rate limiting based on system load
 * - Comprehensive performance metrics and alerting
 * - Automatic retry with exponential backoff
 * - Graceful error handling and recovery
 *
 * @class MySQLManager
 * @since 1.0.0
 */
export class MySQLManager {
  /** Unique session identifier for tracking and debugging */
  private sessionId: string;

  /** Configuration manager for database, security, and cache settings */
  private configManager: ConfigurationManager;

  /** Connection pool manager for efficient database connections */
  private connectionPool: ConnectionPool;

  /** Smart cache for table schema information */
  private schemaCache: SmartCache<any>;

  /** Smart cache for table existence checks */
  private tableExistsCache: SmartCache<boolean>;

  /** Smart cache for index information */
  private indexCache: SmartCache<any>;

  /** Legacy performance metrics collector */
  private metrics: PerformanceMetrics;

  /** Enhanced metrics manager with time-series data and alerting */
  private enhancedMetrics: EnhancedMetricsManager;

  /** Security validator for input sanitization and SQL injection prevention */
  private securityValidator: EnhancedSecurityValidator;

  /** Adaptive rate limiter for request throttling */
  private adaptiveRateLimiter: AdaptiveRateLimiter;

  /** Retry strategy for handling transient errors */
  private retryStrategy: RetryStrategy;

  /**
   * Pre-compiled dangerous SQL patterns for security validation
   * These patterns detect potentially harmful SQL operations that could
   * compromise system security or data integrity.
   */
  private static DANGEROUS_PATTERNS: RegExp[] = [
    /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
    /\b(SYSTEM|EXEC|SHELL)\b/i,
    /\bINTO\s+OUTFILE\b/i,
    /\bLOAD\s+DATA\b/i,
  ];

  /**
   * Table name validation pattern
   * Ensures table names contain only safe characters (alphanumeric, underscore, hyphen)
   */
  private static TABLE_NAME_PATTERN: RegExp = /^[a-zA-Z0-9_-]+$/;
  
  /**
   * MySQL Manager Constructor
   *
   * Initializes all components of the MySQL management system including
   * configuration, connection pooling, caching, security, and monitoring.
   *
   * @constructor
   * @throws {Error} When component initialization fails
   */
  constructor() {
    // Generate unique session identifier for tracking
    this.sessionId = randomUUID();

    // Initialize centralized configuration management
    this.configManager = new ConfigurationManager();

    // Initialize connection pool with database configuration
    this.connectionPool = new ConnectionPool(this.configManager.database);

    // Initialize smart caching system with configured sizes and TTL
    this.schemaCache = new SmartCache(
      this.configManager.cache.schemaCacheSize,
      this.configManager.cache.cacheTTL
    );
    this.tableExistsCache = new SmartCache(
      this.configManager.cache.tableExistsCacheSize,
      this.configManager.cache.cacheTTL
    );
    this.indexCache = new SmartCache(
      this.configManager.cache.indexCacheSize,
      this.configManager.cache.cacheTTL
    );

    // Initialize performance monitoring systems
    this.metrics = new PerformanceMetrics();
    this.enhancedMetrics = new EnhancedMetricsManager();
    this.enhancedMetrics.startMonitoring();

    // Initialize security validation system
    this.securityValidator = new EnhancedSecurityValidator();

    // Initialize adaptive rate limiting with security configuration
    this.adaptiveRateLimiter = new AdaptiveRateLimiter(
      this.configManager.security.rateLimitMax,
      this.configManager.security.rateLimitWindow
    );

    // Initialize retry strategy for error handling
    this.retryStrategy = new DefaultRetryStrategy();
  }
  
  /**
   * Execute Operation with Retry Mechanism
   *
   * Executes database operations with automatic retry using exponential backoff.
   * Handles transient errors while avoiding retries for permanent failures.
   *
   * @private
   * @template T - Return type of the operation
   * @param operation - Async operation to execute with retry logic
   * @returns Promise resolving to operation result
   * @throws {Error} When all retry attempts are exhausted or non-retryable error occurs
   */
  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.retryStrategy.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;

        // Skip retry for certain error types that won't resolve with retries
        if (error.code) {
          const errorCode = parseInt(error.code, 10);
          if (errorCode === MySQLErrorCodes.ACCESS_DENIED ||
              errorCode === MySQLErrorCodes.PARSE_ERROR) {
            break;
          }
        }

        // Apply exponential backoff delay before next attempt
        if (attempt < this.retryStrategy.maxAttempts - 1) {
          const delay = this.retryStrategy.getDelay(attempt) * 1000; // Convert to milliseconds
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error("Operation failed after all retries");
  }

  /**
   * Validate Input Data
   *
   * Performs comprehensive security validation on input data to prevent
   * SQL injection and other security vulnerabilities.
   *
   * @private
   * @param inputValue - Value to validate (string, number, boolean, null, undefined)
   * @param fieldName - Name of the field being validated (for error messages)
   * @param validationLevel - Validation strictness level ("strict", "moderate", "basic")
   * @throws {Error} When input fails security validation
   */
  private validateInput(inputValue: any, fieldName: string, validationLevel: string = "strict"): void {
    EnhancedSecurityValidator.validateInputComprehensive(inputValue, fieldName, validationLevel);
  }
  
  /**
   * Validate SQL Query Security
   *
   * Performs comprehensive security validation on SQL queries including
   * length limits, dangerous pattern detection, and query type restrictions.
   *
   * @private
   * @param query - SQL query string to validate
   * @throws {Error} When query fails security validation
   */
  private validateQuery(query: string): void {
    // Check query length against configured maximum
    if (query.length > this.configManager.security.maxQueryLength) {
      throw new Error(StringConstants.MSG_QUERY_TOO_LONG);
    }

    // Scan for dangerous SQL patterns that could compromise security
    for (const pattern of MySQLManager.DANGEROUS_PATTERNS) {
      if (pattern.test(query)) {
        throw new Error(StringConstants.MSG_PROHIBITED_OPERATIONS);
      }
    }

    // Extract and validate query type (first word)
    const firstWordEnd = query.indexOf(' ');
    const queryType = (firstWordEnd !== -1 ? query.substring(0, firstWordEnd) : query).trim().toUpperCase();

    // Ensure query type is in the allowed list
    if (!this.configManager.security.allowedQueryTypes.includes(queryType)) {
      throw new Error(StringConstants.MSG_QUERY_TYPE_NOT_ALLOWED.replace('{query_type}', queryType));
    }
  }

  /**
   * Validate Table Name
   *
   * Validates table names against security patterns and length restrictions
   * to prevent SQL injection and ensure compatibility.
   *
   * @private
   * @param tableName - Table name to validate
   * @throws {Error} When table name is invalid or too long
   */
  private validateTableName(tableName: string): void {
    // Check against allowed character pattern
    if (!MySQLManager.TABLE_NAME_PATTERN.test(tableName)) {
      throw new Error(StringConstants.MSG_INVALID_TABLE_NAME);
    }

    // Check length limit
    if (tableName.length > DefaultConfig.MAX_TABLE_NAME_LENGTH) {
      throw new Error(StringConstants.MSG_TABLE_NAME_TOO_LONG);
    }
  }

  /**
   * Check Rate Limit
   *
   * Verifies that the current request is within rate limiting bounds
   * using the adaptive rate limiter.
   *
   * @private
   * @param identifier - Unique identifier for rate limiting (defaults to "default")
   * @throws {Error} When rate limit is exceeded
   */
  private checkRateLimit(identifier: string = "default"): void {
    if (!this.adaptiveRateLimiter.checkRateLimit(identifier)) {
      throw new Error(StringConstants.MSG_RATE_LIMIT_EXCEEDED);
    }
  }
  
  /**
   * Get Table Schema with Caching
   *
   * Retrieves table schema information with intelligent caching to improve
   * performance. Cache misses trigger database queries while hits return
   * cached data immediately.
   *
   * @private
   * @param tableName - Name of the table to get schema for
   * @returns Promise resolving to table schema information
   * @throws {Error} When schema query fails
   */
  private async getTableSchemaCached(tableName: string): Promise<any> {
    const cacheKey = `schema_${tableName}`;
    let result = this.schemaCache.get(cacheKey);

    if (result === null) {
      // Cache miss: execute schema query
      const schemaQuery = `
        SELECT
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `;

      result = await this.executeQuery(schemaQuery, [tableName]);
      this.schemaCache.put(cacheKey, result);
      this.metrics.cacheMisses++;
    } else {
      // Cache hit: return cached data
      this.metrics.cacheHits++;
    }

    return result ?? false;
  }

  /**
   * Check Table Existence with Caching
   *
   * Verifies if a table exists in the current database with caching
   * to avoid repeated INFORMATION_SCHEMA queries.
   *
   * @private
   * @param tableName - Name of the table to check
   * @returns Promise resolving to true if table exists, false otherwise
   * @throws {Error} When existence check query fails
   */
  private async tableExistsCached(tableName: string): Promise<boolean> {
    const cacheKey = `exists_${tableName}`;
    let result = this.tableExistsCache.get(cacheKey);

    if (result === null) {
      // Cache miss: execute existence check query
      const existsQuery = `
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
      `;

      const queryResult = await this.executeQuery(existsQuery, [tableName]);
      result = queryResult && queryResult[0] && queryResult[0].count > 0;
      this.tableExistsCache.put(cacheKey, result ?? false);
      this.metrics.cacheMisses++;
    } else {
      // Cache hit: return cached result
      this.metrics.cacheHits++;
    }

    return result ?? false;
  }
  
  /**
   * Execute SQL Query
   *
   * Main public method for executing SQL queries with comprehensive security,
   * performance monitoring, caching, and error handling. Includes rate limiting,
   * retry mechanisms, and metrics collection.
   *
   * @public
   * @param query - SQL query string to execute
   * @param params - Optional parameters for prepared statements
   * @returns Promise resolving to query results
   * @throws {Error} When rate limit exceeded, security validation fails, or query execution fails
   *
   * @example
   * // Simple query
   * const results = await manager.executeQuery("SELECT * FROM users LIMIT 10");
   *
   * @example
   * // Parameterized query
   * const user = await manager.executeQuery("SELECT * FROM users WHERE id = ?", [123]);
   */
  public async executeQuery(query: string, params?: any[]): Promise<any> {
    const startTime = Date.now();

    try {
      // Apply rate limiting to prevent abuse
      this.checkRateLimit();

      // Validate query for security compliance
      this.validateQuery(query);

      // Execute with automatic retry on transient failures
      const result = await this.executeWithRetry(async () => {
        return await this.executeQueryInternal(query, params);
      });

      // Record successful execution metrics
      const queryTime = (Date.now() - startTime) / 1000; // Convert to seconds
      const isSlow = queryTime > DefaultConfig.SLOW_QUERY_THRESHOLD;
      this.updateMetrics(queryTime, false, isSlow);

      return result;
    } catch (error) {
      // Record error metrics
      const queryTime = (Date.now() - startTime) / 1000; // Convert to seconds
      this.updateMetrics(queryTime, true, false);
      throw error;
    }
  }

  /**
   * Internal Query Execution
   *
   * Low-level method that handles the actual database connection and query execution.
   * Manages connection lifecycle and ensures proper resource cleanup.
   *
   * @private
   * @param query - SQL query string
   * @param params - Optional query parameters
   * @returns Promise resolving to raw query results
   * @throws {Error} When connection or query execution fails
   */
  private async executeQueryInternal(query: string, params?: any[]): Promise<any> {
    const connection = await this.connectionPool.getConnection();

    try {
      const [rows] = await connection.execute(query, params);
      return rows;
    } finally {
      // Always release connection back to pool
      connection.release();
    }
  }
  
  // Update performance metrics
  private updateMetrics(queryTime: number, isError: boolean = false, isSlow: boolean = false): void {
    this.metrics.queryCount++;
    this.metrics.totalQueryTime += queryTime;
    
    if (isError) {
      this.metrics.errorCount++;
      this.enhancedMetrics.recordError("query_error", "medium");
    }
    
    if (isSlow) {
      this.metrics.slowQueryCount++;
    }
    
    // Record to enhanced metrics manager
    this.enhancedMetrics.recordQueryTime(queryTime);
    
    // Update cache hit rate metrics
    const cacheHitRate = this.metrics.getCacheHitRate();
    this.enhancedMetrics.recordCacheHitRate(cacheHitRate);
  }
  
  // Invalidate caches
  public invalidateCaches(operationType: string = "DDL", tableName?: string): void {
    if (operationType === "DDL" || operationType === "CREATE" || operationType === "DROP" || operationType === "ALTER") {
      // DDL operations clear all caches
      this.clearAllCaches();
    } else if (operationType === "DML" && tableName) {
      // DML operations clear specific table cache
      this.invalidateTableSpecificCache(tableName);
    }
  }
  
  // Clear all caches
  private clearAllCaches(): void {
    this.schemaCache.clear();
    this.tableExistsCache.clear();
    this.indexCache.clear();
  }
  
  // Invalidate specific table cache
  private invalidateTableSpecificCache(tableName: string): void {
    const cacheKeysToRemove = [
      `schema_${tableName}`,
      `exists_${tableName}`,
      `indexes_${tableName}`
    ];
    
    // Remove keys from all caches
    cacheKeysToRemove.forEach(key => {
      // We can't directly access the cache internals, so we'll just clear them
      // In a real implementation, we would have a method to remove specific keys
    });
  }
  
  /**
   * Get Performance Metrics
   *
   * Retrieves comprehensive performance metrics including query statistics,
   * cache performance, and connection pool status for monitoring and debugging.
   *
   * @public
   * @returns Object containing detailed performance metrics
   *
   * @example
   * const metrics = manager.getPerformanceMetrics();
   * console.log(`Cache hit rate: ${metrics.cache_stats.schema_cache.hit_rate}`);
   */
  public getPerformanceMetrics(): Record<string, any> {
    return {
      [StringConstants.SECTION_PERFORMANCE]: this.metrics.toObject(),
      [StringConstants.SECTION_CACHE_STATS]: {
        [StringConstants.SECTION_SCHEMA_CACHE]: this.schemaCache.getStats(),
        [StringConstants.SECTION_TABLE_EXISTS_CACHE]: this.tableExistsCache.getStats(),
        [StringConstants.SECTION_INDEX_CACHE]: this.indexCache.getStats()
      },
      [StringConstants.SECTION_CONNECTION_POOL]: this.connectionPool.getStats()
    };
  }

  /**
   * Close MySQL Manager
   *
   * Performs graceful shutdown of all components including metrics monitoring,
   * connection pool closure, and cache cleanup. Should be called during
   * application shutdown to prevent resource leaks.
   *
   * @public
   * @returns Promise that resolves when all cleanup is complete
   *
   * @example
   * // Graceful shutdown
   * await manager.close();
   */
  public async close(): Promise<void> {
    try {
      // Stop enhanced metrics monitoring
      this.enhancedMetrics.stopMonitoring();

      // Close connection pool and release all connections
      await this.connectionPool.close();

      // Clear all caches to free memory
      this.schemaCache.clear();
      this.tableExistsCache.clear();
      this.indexCache.clear();
    } catch (error) {
      console.error(`${StringConstants.MSG_ERROR_DURING_CLEANUP} ${error}`);
    }
  }
}