/**
 * Enhanced Security Validator
 *
 * Comprehensive security validation system for preventing SQL injection,
 * command injection, and other security vulnerabilities. Implements multiple
 * layers of protection including pattern matching, input sanitization,
 * and encoding validation.
 *
 * @fileoverview Advanced security validation for MySQL operations
 * <AUTHOR> MCP Team
 * @since 1.0.0
 */

import { DefaultConfig, StringConstants } from './constants.js';

/**
 * Enhanced Security Validator Class
 *
 * Multi-layered security validation system that protects against:
 * - SQL injection attacks
 * - Command injection attempts
 * - File system access exploits
 * - Information disclosure attacks
 * - Timing attacks and DoS attempts
 *
 * Security Features:
 * - Pre-compiled regex patterns for performance
 * - Multiple validation levels (strict, moderate, basic)
 * - Character encoding validation
 * - Control character detection
 * - Length limit enforcement
 *
 * @class EnhancedSecurityValidator
 * @since 1.0.0
 */
export class EnhancedSecurityValidator {
  /**
   * Dangerous SQL Patterns
   *
   * Pre-compiled regex patterns that detect potentially dangerous SQL operations
   * that could compromise system security or data integrity:
   *
   * - LOAD_FILE, INTO OUTFILE, INTO DUMPFILE: File system access
   * - SYSTEM, EXEC, SHELL, xp_cmdshell: Command execution
   * - UNION SELECT with INFORMATION_SCHEMA: Information disclosure
   * - Stacked queries with DDL operations: Schema manipulation
   * - BENCHMARK, SLEEP, WAITFOR: Timing attacks and DoS
   * - System variables access: Configuration disclosure
   */
  private static DANGEROUS_PATTERNS: RegExp[] = [
    /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
    /\b(SYSTEM|EXEC|SHELL|xp_cmdshell)\b/i,
    /\b(UNION\s+SELECT).*(\bFROM\s+INFORMATION_SCHEMA)\b/i,
    /;\s*(DROP|DELETE|TRUNCATE|ALTER)\b/i,
    /\b(BENCHMARK|SLEEP|WAITFOR)\s*\(/i,
    /@@(version|datadir|basedir|tmpdir)/i,
  ];

  /**
   * SQL Injection Patterns
   *
   * Pre-compiled regex patterns that detect common SQL injection techniques:
   *
   * - Quote-based injection with OR/AND conditions
   * - Union-based injection attempts
   * - Boolean-based blind injection
   * - Numeric injection with comparison operators
   * - Classic authentication bypass patterns
   */
  private static INJECTION_PATTERNS: RegExp[] = [
    /(\s|^)('|")\s*(OR|AND)\s*(\d+|'[^']*'|\")[^><=!]*(\s)*[><=!]{1,2}.*/i,
    /('|").*(\s|^)(UNION|SELECT|INSERT|DELETE|UPDATE|DROP|CREATE|ALTER)(\s)/i,
    /\s*(OR|AND)\s+[\w'"]+\s*[><=!]+.*/i,
    /('\s*OR\s*'\d+'\s*=\s*'\d')/i,
    /("\s*OR\s*"\d+"\s*=\s*"\d")/i,
  ];

  /**
   * Comprehensive Input Validation
   *
   * Main validation entry point that performs type checking and delegates
   * to specialized validators based on input type. Supports multiple
   * validation levels for different security requirements.
   *
   * Validation Levels:
   * - "strict": Full security validation with all patterns
   * - "moderate": Essential security checks only
   * - "basic": Minimal validation for performance-critical paths
   *
   * @public
   * @static
   * @param {any} inputValue - Value to validate (any type)
   * @param {string} fieldName - Name of the field for error messages
   * @param {string} [validationLevel="strict"] - Validation strictness level
   * @throws {Error} When input fails validation checks
   *
   * @example
   * // Strict validation (default)
   * EnhancedSecurityValidator.validateInputComprehensive(userInput, "username");
   *
   * @example
   * // Moderate validation for performance
   * EnhancedSecurityValidator.validateInputComprehensive(data, "field", "moderate");
   */
  public static validateInputComprehensive(inputValue: any, fieldName: string, validationLevel: string = "strict"): void {
    // Type validation: ensure input is of acceptable type
    if (typeof inputValue !== 'string' && typeof inputValue !== 'number' && typeof inputValue !== 'boolean' && inputValue !== null && inputValue !== undefined) {
      throw new Error(`${fieldName} has invalid data type`);
    }

    // String-specific validation (most security-critical)
    if (typeof inputValue === 'string') {
      this.validateStringComprehensive(inputValue, fieldName, validationLevel);
    }
  }

  /**
   * Enhanced String Validation
   *
   * Comprehensive string validation including control character detection,
   * length limits, encoding validation, and security pattern matching.
   * Implements multiple security layers to prevent various attack vectors.
   *
   * Security Checks:
   * 1. Control character detection (prevents binary injection)
   * 2. Length limit enforcement (prevents buffer overflow)
   * 3. Character encoding validation (prevents encoding attacks)
   * 4. Dangerous pattern detection (prevents SQL injection)
   * 5. Injection pattern matching (prevents various injection types)
   *
   * @private
   * @static
   * @param {string} value - String value to validate
   * @param {string} fieldName - Field name for error messages
   * @param {string} level - Validation level ("strict", "moderate", "basic")
   * @throws {Error} When string fails any validation check
   */
  private static validateStringComprehensive(value: string, fieldName: string, level: string): void {
    // Control character validation (security: prevent binary injection)
    if (value.split('').some(c => c.charCodeAt(0) < 32 && !['\t', '\n', '\r'].includes(c))) {
      throw new Error(`${fieldName} contains invalid control characters`);
    }

    // Length validation (security: prevent buffer overflow attacks)
    if (value.length > DefaultConfig.MAX_INPUT_LENGTH) {
      throw new Error(`${fieldName} exceeds maximum length limit`);
    }

    // Character encoding validation (security: prevent encoding attacks)
    try {
      Buffer.from(value, 'utf-8');
    } catch (e) {
      throw new Error(`${fieldName} contains invalid character encoding`);
    }

    // Pattern-based security validation
    if (level === "strict") {
      // Full security validation: check all dangerous patterns
      if (this.DANGEROUS_PATTERNS.some(pattern => pattern.test(value))) {
        throw new Error(`${fieldName} contains potentially dangerous content`);
      }

      // SQL injection pattern detection
      if (this.INJECTION_PATTERNS.some(pattern => pattern.test(value))) {
        throw new Error(`${fieldName} contains potential SQL injection attempt`);
      }
    } else if (level === "moderate") {
      // Moderate validation: check only the most critical patterns
      const criticalPatterns = this.DANGEROUS_PATTERNS.slice(0, 3);
      if (criticalPatterns.some(pattern => pattern.test(value))) {
        throw new Error(`${fieldName} contains dangerous content`);
      }
    }
    // "basic" level: skip pattern validation for performance
  }
}