{"name": "mcp-proxy", "version": "3.3.0", "main": "dist/index.js", "scripts": {"build": "tsup", "test": "vitest run && tsc && eslint . && jsr publish --dry-run --allow-dirty", "format": "eslint --fix ."}, "bin": {"mcp-proxy": "dist/bin/mcp-proxy.js"}, "keywords": ["MCP", "SSE", "proxy"], "type": "module", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "description": "A TypeScript SSE proxy for MCP servers that use stdio transport.", "module": "dist/index.js", "types": "dist/index.d.ts", "dependencies": {"@modelcontextprotocol/sdk": "^1.11.4", "eventsource": "^4.0.0", "yargs": "^17.7.2"}, "repository": {"url": "https://github.com/punkpeye/mcp-proxy"}, "homepage": "https://glama.ai/mcp", "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@sebbo2002/semantic-release-jsr"]}, "devDependencies": {"@eslint/js": "^9.27.0", "@sebbo2002/semantic-release-jsr": "^3.0.0", "@tsconfig/node22": "^22.0.2", "@types/express": "^5.0.2", "@types/node": "^22.15.21", "@types/yargs": "^17.0.33", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-perfectionist": "^4.13.0", "express": "^5.0.1", "get-port-please": "^3.1.2", "jiti": "^2.4.2", "jsr": "^0.13.4", "prettier": "^3.5.3", "semantic-release": "^24.2.4", "tsup": "^8.5.0", "tsx": "^4.19.3", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vitest": "^3.1.4"}, "tsup": {"entry": ["src/index.ts", "src/bin/mcp-proxy.ts"], "format": ["esm"], "dts": true, "splitting": true, "sourcemap": true, "clean": true}}