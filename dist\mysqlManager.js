// MySQL Manager - Main class integrating all components
import { randomUUID } from 'crypto';
import { ConnectionPool } from './connection.js';
import { ConfigurationManager } from './config.js';
import { SmartCache } from './cache.js';
import { EnhancedSecurityValidator } from './security.js';
import { AdaptiveRateLimiter } from './rateLimit.js';
import { EnhancedMetricsManager, PerformanceMetrics } from './metrics.js';
import { StringConstants, DefaultConfig, MySQLErrorCodes } from './constants.js';
// Default retry strategy
class DefaultRetryStrategy {
    maxAttempts = DefaultConfig.MAX_RETRY_ATTEMPTS;
    baseDelay = 1.0;
    maxDelay = 10.0;
    backoffFactor = 2.0;
    getDelay(attempt) {
        const delay = this.baseDelay * Math.pow(this.backoffFactor, attempt);
        return Math.min(delay, this.maxDelay);
    }
}
export class MySQLManager {
    sessionId;
    configManager;
    connectionPool;
    schemaCache;
    tableExistsCache;
    indexCache;
    metrics;
    enhancedMetrics;
    securityValidator;
    adaptiveRateLimiter;
    retryStrategy;
    // Pre-compiled regex patterns
    static DANGEROUS_PATTERNS = [
        /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
        /\b(SYSTEM|EXEC|SHELL)\b/i,
        /\bINTO\s+OUTFILE\b/i,
        /\bLOAD\s+DATA\b/i,
    ];
    static TABLE_NAME_PATTERN = /^[a-zA-Z0-9_-]+$/;
    constructor() {
        this.sessionId = randomUUID();
        // Centralized configuration management
        this.configManager = new ConfigurationManager();
        // Connection pool
        this.connectionPool = new ConnectionPool(this.configManager.database);
        // Smart caches
        this.schemaCache = new SmartCache(this.configManager.cache.schemaCacheSize, this.configManager.cache.cacheTTL);
        this.tableExistsCache = new SmartCache(this.configManager.cache.tableExistsCacheSize, this.configManager.cache.cacheTTL);
        this.indexCache = new SmartCache(this.configManager.cache.indexCacheSize, this.configManager.cache.cacheTTL);
        // Performance metrics
        this.metrics = new PerformanceMetrics();
        this.enhancedMetrics = new EnhancedMetricsManager();
        this.enhancedMetrics.startMonitoring();
        // Security validator
        this.securityValidator = new EnhancedSecurityValidator();
        // Adaptive rate limiter
        this.adaptiveRateLimiter = new AdaptiveRateLimiter(this.configManager.security.rateLimitMax, this.configManager.security.rateLimitWindow);
        // Retry strategy
        this.retryStrategy = new DefaultRetryStrategy();
    }
    // Execute query with retry mechanism
    async executeWithRetry(operation) {
        let lastError = null;
        for (let attempt = 0; attempt < this.retryStrategy.maxAttempts; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error;
                // Certain errors should not be retried
                if (error.code) {
                    const errorCode = parseInt(error.code, 10);
                    if (errorCode === MySQLErrorCodes.ACCESS_DENIED ||
                        errorCode === MySQLErrorCodes.PARSE_ERROR) {
                        break;
                    }
                }
                if (attempt < this.retryStrategy.maxAttempts - 1) {
                    const delay = this.retryStrategy.getDelay(attempt) * 1000; // Convert to milliseconds
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError || new Error("Operation failed after all retries");
    }
    // Validate input
    validateInput(inputValue, fieldName, validationLevel = "strict") {
        EnhancedSecurityValidator.validateInputComprehensive(inputValue, fieldName, validationLevel);
    }
    // Validate SQL query
    validateQuery(query) {
        if (query.length > this.configManager.security.maxQueryLength) {
            throw new Error(StringConstants.MSG_QUERY_TOO_LONG);
        }
        // Check dangerous patterns
        for (const pattern of MySQLManager.DANGEROUS_PATTERNS) {
            if (pattern.test(query)) {
                throw new Error(StringConstants.MSG_PROHIBITED_OPERATIONS);
            }
        }
        // Get query type (first word)
        const firstWordEnd = query.indexOf(' ');
        const queryType = (firstWordEnd !== -1 ? query.substring(0, firstWordEnd) : query).trim().toUpperCase();
        if (!this.configManager.security.allowedQueryTypes.includes(queryType)) {
            throw new Error(StringConstants.MSG_QUERY_TYPE_NOT_ALLOWED.replace('{query_type}', queryType));
        }
    }
    // Validate table name
    validateTableName(tableName) {
        if (!MySQLManager.TABLE_NAME_PATTERN.test(tableName)) {
            throw new Error(StringConstants.MSG_INVALID_TABLE_NAME);
        }
        if (tableName.length > DefaultConfig.MAX_TABLE_NAME_LENGTH) {
            throw new Error(StringConstants.MSG_TABLE_NAME_TOO_LONG);
        }
    }
    // Check rate limit
    checkRateLimit(identifier = "default") {
        if (!this.adaptiveRateLimiter.checkRateLimit(identifier)) {
            throw new Error(StringConstants.MSG_RATE_LIMIT_EXCEEDED);
        }
    }
    // Get table schema with caching
    async getTableSchemaCached(tableName) {
        const cacheKey = `schema_${tableName}`;
        let result = this.schemaCache.get(cacheKey);
        if (result === null) {
            // Schema query
            const schemaQuery = `
        SELECT 
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `;
            result = await this.executeQuery(schemaQuery, [tableName]);
            this.schemaCache.put(cacheKey, result);
            this.metrics.cacheMisses++;
        }
        else {
            this.metrics.cacheHits++;
        }
        return result ?? false;
    }
    // Check if table exists with caching
    async tableExistsCached(tableName) {
        const cacheKey = `exists_${tableName}`;
        let result = this.tableExistsCache.get(cacheKey);
        if (result === null) {
            // Table existence query
            const existsQuery = `
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
      `;
            const queryResult = await this.executeQuery(existsQuery, [tableName]);
            result = queryResult && queryResult[0] && queryResult[0].count > 0;
            this.tableExistsCache.put(cacheKey, result ?? false);
            this.metrics.cacheMisses++;
        }
        else {
            this.metrics.cacheHits++;
        }
        return result ?? false;
    }
    // Execute SQL query
    async executeQuery(query, params) {
        const startTime = Date.now();
        try {
            // Check rate limit
            this.checkRateLimit();
            // Validate query security
            this.validateQuery(query);
            // Execute with retry mechanism
            const result = await this.executeWithRetry(async () => {
                return await this.executeQueryInternal(query, params);
            });
            // Update performance metrics
            const queryTime = (Date.now() - startTime) / 1000; // Convert to seconds
            const isSlow = queryTime > DefaultConfig.SLOW_QUERY_THRESHOLD;
            this.updateMetrics(queryTime, false, isSlow);
            return result;
        }
        catch (error) {
            const queryTime = (Date.now() - startTime) / 1000; // Convert to seconds
            this.updateMetrics(queryTime, true, false);
            throw error;
        }
    }
    // Internal query execution
    async executeQueryInternal(query, params) {
        const connection = await this.connectionPool.getConnection();
        try {
            const [rows, fields] = await connection.execute(query, params);
            return rows;
        }
        finally {
            connection.release();
        }
    }
    // Update performance metrics
    updateMetrics(queryTime, isError = false, isSlow = false) {
        this.metrics.queryCount++;
        this.metrics.totalQueryTime += queryTime;
        if (isError) {
            this.metrics.errorCount++;
            this.enhancedMetrics.recordError("query_error", "medium");
        }
        if (isSlow) {
            this.metrics.slowQueryCount++;
        }
        // Record to enhanced metrics manager
        this.enhancedMetrics.recordQueryTime(queryTime);
        // Update cache hit rate metrics
        const cacheHitRate = this.metrics.getCacheHitRate();
        this.enhancedMetrics.recordCacheHitRate(cacheHitRate);
    }
    // Invalidate caches
    invalidateCaches(operationType = "DDL", tableName) {
        if (operationType === "DDL" || operationType === "CREATE" || operationType === "DROP" || operationType === "ALTER") {
            // DDL operations clear all caches
            this.clearAllCaches();
        }
        else if (operationType === "DML" && tableName) {
            // DML operations clear specific table cache
            this.invalidateTableSpecificCache(tableName);
        }
    }
    // Clear all caches
    clearAllCaches() {
        this.schemaCache.clear();
        this.tableExistsCache.clear();
        this.indexCache.clear();
    }
    // Invalidate specific table cache
    invalidateTableSpecificCache(tableName) {
        const cacheKeysToRemove = [
            `schema_${tableName}`,
            `exists_${tableName}`,
            `indexes_${tableName}`
        ];
        // Remove keys from all caches
        cacheKeysToRemove.forEach(key => {
            // We can't directly access the cache internals, so we'll just clear them
            // In a real implementation, we would have a method to remove specific keys
        });
    }
    // Get performance metrics
    getPerformanceMetrics() {
        return {
            [StringConstants.SECTION_PERFORMANCE]: this.metrics.toObject(),
            [StringConstants.SECTION_CACHE_STATS]: {
                [StringConstants.SECTION_SCHEMA_CACHE]: this.schemaCache.getStats(),
                [StringConstants.SECTION_TABLE_EXISTS_CACHE]: this.tableExistsCache.getStats(),
                [StringConstants.SECTION_INDEX_CACHE]: this.indexCache.getStats()
            },
            [StringConstants.SECTION_CONNECTION_POOL]: this.connectionPool.getStats()
        };
    }
    // Close manager
    async close() {
        try {
            // Stop enhanced metrics monitoring
            this.enhancedMetrics.stopMonitoring();
            // Close connection pool
            await this.connectionPool.close();
            // Clear caches
            this.schemaCache.clear();
            this.tableExistsCache.clear();
            this.indexCache.clear();
        }
        catch (error) {
            console.error(`${StringConstants.MSG_ERROR_DURING_CLEANUP} ${error}`);
        }
    }
}
//# sourceMappingURL=mysqlManager.js.map