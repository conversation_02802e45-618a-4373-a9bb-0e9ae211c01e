export interface DatabaseConfig {
    host: string;
    port: number;
    user: string;
    password: string;
    database: string;
    connectionLimit: number;
    minConnections: number;
    connectTimeout: number;
    idleTimeout: number;
    sslEnabled: boolean;
}
export interface SecurityConfig {
    maxQueryLength: number;
    allowedQueryTypes: string[];
    maxResultRows: number;
    queryTimeout: number;
    rateLimitMax: number;
    rateLimitWindow: number;
}
export interface CacheConfig {
    schemaCacheSize: number;
    tableExistsCacheSize: number;
    indexCacheSize: number;
    cacheTTL: number;
}
export declare class ConfigurationManager {
    database: DatabaseConfig;
    security: SecurityConfig;
    cache: CacheConfig;
    constructor();
    private loadDatabaseConfig;
    private loadSecurityConfig;
    private loadCacheConfig;
    toObject(): Record<string, any>;
    getSummary(): Record<string, string>;
}
//# sourceMappingURL=config.d.ts.map