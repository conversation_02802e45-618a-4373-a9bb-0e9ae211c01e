{"version": 3, "sources": ["../../src/bin/mcp-proxy.ts", "../../src/StdioClientTransport.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { Client } from \"@modelcontextprotocol/sdk/client/index.js\";\nimport { Server } from \"@modelcontextprotocol/sdk/server/index.js\";\nimport { EventSource } from \"eventsource\";\nimport { setTimeout } from \"node:timers\";\nimport util from \"node:util\";\nimport yargs from \"yargs\";\nimport { hideBin } from \"yargs/helpers\";\n\nimport { InMemoryEventStore } from \"../InMemoryEventStore.js\";\nimport { proxyServer } from \"../proxyServer.js\";\nimport { startHTTPServer } from \"../startHTTPServer.js\";\nimport { StdioClientTransport } from \"../StdioClientTransport.js\";\n\nutil.inspect.defaultOptions.depth = 8;\n\nif (!(\"EventSource\" in global)) {\n  // @ts-expect-error - figure out how to use --experimental-eventsource with vitest\n  global.EventSource = EventSource;\n}\n\nconst argv = await yargs(hideBin(process.argv))\n  .scriptName(\"mcp-proxy\")\n  .command(\"$0 <command> [args...]\", \"Run a command with MCP arguments\")\n  .positional(\"command\", {\n    demandOption: true,\n    describe: \"The command to run\",\n    type: \"string\",\n  })\n  .positional(\"args\", {\n    array: true,\n    describe: \"The arguments to pass to the command\",\n    type: \"string\",\n  })\n  .env(\"MCP_PROXY\")\n  .options({\n    debug: {\n      default: false,\n      describe: \"Enable debug logging\",\n      type: \"boolean\",\n    },\n    endpoint: {\n      describe: \"The endpoint to listen on\",\n      type: \"string\",\n    },\n    port: {\n      default: 8080,\n      describe: \"The port to listen on\",\n      type: \"number\",\n    },\n    server: {\n      choices: [\"sse\", \"stream\"],\n      describe: \"The server type to use (sse or stream). By default, both are enabled\",\n      type: \"string\",\n    },\n    shell: {\n      default: false,\n      describe: \"Spawn the server via the user's shell\",\n      type: \"boolean\",\n    },\n    sseEndpoint: {\n      default: \"/sse\",\n      describe: \"The SSE endpoint to listen on\",\n      type: \"string\",\n    },\n    streamEndpoint: {\n      default: \"/stream\",\n      describe: \"The stream endpoint to listen on\",\n      type: \"string\",\n    },\n  })\n  .help()\n  .parseAsync();\n\nconst connect = async (client: Client) => {\n  const transport = new StdioClientTransport({\n    args: argv.args,\n    command: argv.command,\n    env: process.env as Record<string, string>,\n    onEvent: (event) => {\n      if (argv.debug) {\n        console.debug(\"transport event\", event);\n      }\n    },\n    shell: argv.shell,\n    stderr: \"pipe\",\n  });\n\n  await client.connect(transport);\n};\n\nconst proxy = async () => {\n  const client = new Client(\n    {\n      name: \"mcp-proxy\",\n      version: \"1.0.0\",\n    },\n    {\n      capabilities: {},\n    },\n  );\n\n  await connect(client);\n\n  const serverVersion = client.getServerVersion() as {\n    name: string;\n    version: string;\n  };\n\n  const serverCapabilities = client.getServerCapabilities() as {\n    capabilities: Record<string, unknown>;\n  };\n\n  console.info(\"starting server on port %d\", argv.port);\n\n  const createServer = async () => {\n    const server = new Server(serverVersion, {\n      capabilities: serverCapabilities,\n    });\n\n    proxyServer({\n      client,\n      server,\n      serverCapabilities,\n    });\n\n    return server;\n  };\n\n  await startHTTPServer({\n    createServer,\n    eventStore: new InMemoryEventStore(),\n    port: argv.port,\n    sseEndpoint: argv.server && argv.server !== \"sse\" ? null : (argv.sseEndpoint ?? argv.endpoint),\n    streamEndpoint: argv.server && argv.server !== \"stream\" ? null : (argv.streamEndpoint ?? argv.endpoint),\n  });\n};\n\nconst main = async () => {\n  process.on(\"SIGINT\", () => {\n    console.info(\"SIGINT received, shutting down\");\n\n    setTimeout(() => {\n      process.exit(0);\n    }, 1000);\n  });\n\n  try {\n    await proxy();\n  } catch (error) {\n    console.error(\"could not start the proxy\", error);\n\n    setTimeout(() => {\n      process.exit(1);\n    }, 1000);\n  }\n};\n\nawait main();\n", "/**\n * Forked from https://github.com/modelcontextprotocol/typescript-sdk/blob/66e1508162d37c0b83b0637ebcd7f07946e3d210/src/client/stdio.ts#L90\n */\n\nimport {\n  ReadBuffer,\n  serializeMessage,\n} from \"@modelcontextprotocol/sdk/shared/stdio.js\";\nimport { Transport } from \"@modelcontextprotocol/sdk/shared/transport.js\";\nimport { JSONRPCMessage } from \"@modelcontextprotocol/sdk/types.js\";\nimport { ChildProcess, IOType, spawn } from \"node:child_process\";\nimport { Stream } from \"node:stream\";\n\nexport type StdioServerParameters = {\n  /**\n   * Command line arguments to pass to the executable.\n   */\n  args?: string[];\n\n  /**\n   * The executable to run to start the server.\n   */\n  command: string;\n\n  /**\n   * The working directory to use when spawning the process.\n   *\n   * If not specified, the current working directory will be inherited.\n   */\n  cwd?: string;\n\n  /**\n   * The environment to use when spawning the process.\n   *\n   * If not specified, the result of getDefaultEnvironment() will be used.\n   */\n  env: Record<string, string>;\n\n  /**\n   * A function to call when an event occurs.\n   */\n  onEvent?: (event: TransportEvent) => void;\n\n  /**\n   * When true, spawn the child process using the user's shell.\n   */\n  shell?: boolean;\n\n  /**\n   * How to handle stderr of the child process. This matches the semantics of Node's `child_process.spawn`.\n   *\n   * The default is \"inherit\", meaning messages to stderr will be printed to the parent process's stderr.\n   */\n  stderr?: IOType | number | Stream;\n};\n\ntype TransportEvent =\n  | {\n      chunk: string;\n      type: \"data\";\n    }\n  | {\n      error: Error;\n      type: \"error\";\n    }\n  | {\n      message: JSONRPCMessage;\n      type: \"message\";\n    }\n  | {\n      type: \"close\";\n    };\n\n/**\n * Client transport for stdio: this will connect to a server by spawning a process and communicating with it over stdin/stdout.\n *\n * This transport is only available in Node.js environments.\n */\nexport class StdioClientTransport implements Transport {\n  onclose?: () => void;\n  onerror?: (error: Error) => void;\n  onmessage?: (message: JSONRPCMessage) => void;\n  /**\n   * The stderr stream of the child process, if `StdioServerParameters.stderr` was set to \"pipe\" or \"overlapped\".\n   *\n   * This is only available after the process has been started.\n   */\n  get stderr(): null | Stream {\n    return this.process?.stderr ?? null;\n  }\n  private abortController: AbortController = new AbortController();\n\n  private onEvent?: (event: TransportEvent) => void;\n  private process?: ChildProcess;\n  private readBuffer: ReadBuffer = new ReadBuffer();\n\n  private serverParams: StdioServerParameters;\n\n  constructor(server: StdioServerParameters) {\n    this.serverParams = server;\n    this.onEvent = server.onEvent;\n  }\n\n  async close(): Promise<void> {\n    this.onEvent?.({\n      type: \"close\",\n    });\n\n    this.abortController.abort();\n    this.process = undefined;\n    this.readBuffer.clear();\n  }\n\n  send(message: JSONRPCMessage): Promise<void> {\n    return new Promise((resolve) => {\n      if (!this.process?.stdin) {\n        throw new Error(\"Not connected\");\n      }\n\n      const json = serializeMessage(message);\n      if (this.process.stdin.write(json)) {\n        resolve();\n      } else {\n        this.process.stdin.once(\"drain\", resolve);\n      }\n    });\n  }\n\n  /**\n   * Starts the server process and prepares to communicate with it.\n   */\n  async start(): Promise<void> {\n    if (this.process) {\n      throw new Error(\n        \"StdioClientTransport already started! If using Client class, note that connect() calls start() automatically.\",\n      );\n    }\n\n    return new Promise((resolve, reject) => {\n      this.process = spawn(\n        this.serverParams.command,\n        this.serverParams.args ?? [],\n        {\n          cwd: this.serverParams.cwd,\n          env: this.serverParams.env,\n          shell: this.serverParams.shell ?? false,\n          signal: this.abortController.signal,\n          stdio: [\"pipe\", \"pipe\", this.serverParams.stderr ?? \"inherit\"],\n        },\n      );\n\n      this.process.on(\"error\", (error) => {\n        if (error.name === \"AbortError\") {\n          // Expected when close() is called.\n          this.onclose?.();\n          return;\n        }\n\n        reject(error);\n        this.onerror?.(error);\n      });\n\n      this.process.on(\"spawn\", () => {\n        resolve();\n      });\n\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      this.process.on(\"close\", (_code) => {\n        this.onEvent?.({\n          type: \"close\",\n        });\n\n        this.process = undefined;\n        this.onclose?.();\n      });\n\n      this.process.stdin?.on(\"error\", (error) => {\n        this.onEvent?.({\n          error,\n          type: \"error\",\n        });\n\n        this.onerror?.(error);\n      });\n\n      this.process.stdout?.on(\"data\", (chunk) => {\n        this.onEvent?.({\n          chunk: chunk.toString(),\n          type: \"data\",\n        });\n\n        this.readBuffer.append(chunk);\n        this.processReadBuffer();\n      });\n\n      this.process.stdout?.on(\"error\", (error) => {\n        this.onEvent?.({\n          error,\n          type: \"error\",\n        });\n\n        this.onerror?.(error);\n      });\n    });\n  }\n\n  private processReadBuffer() {\n    while (true) {\n      try {\n        const message = this.readBuffer.readMessage();\n\n        if (message === null) {\n          break;\n        }\n\n        this.onEvent?.({\n          message,\n          type: \"message\",\n        });\n\n        this.onmessage?.(message);\n      } catch (error) {\n        this.onEvent?.({\n          error: error as Error,\n          type: \"error\",\n        });\n\n        this.onerror?.(error as Error);\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAEA,SAAS,cAAc;AACvB,SAAS,cAAc;AACvB,SAAS,mBAAmB;AAC5B,SAAS,kBAAkB;AAC3B,OAAO,UAAU;AACjB,OAAO,WAAW;AAClB,SAAS,eAAe;;;ACJxB;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAGP,SAA+B,aAAa;AAoErC,IAAM,uBAAN,MAAgD;AAAA,EACrD;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAwB;AAC1B,WAAO,KAAK,SAAS,UAAU;AAAA,EACjC;AAAA,EACQ,kBAAmC,IAAI,gBAAgB;AAAA,EAEvD;AAAA,EACA;AAAA,EACA,aAAyB,IAAI,WAAW;AAAA,EAExC;AAAA,EAER,YAAY,QAA+B;AACzC,SAAK,eAAe;AACpB,SAAK,UAAU,OAAO;AAAA,EACxB;AAAA,EAEA,MAAM,QAAuB;AAC3B,SAAK,UAAU;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAED,SAAK,gBAAgB,MAAM;AAC3B,SAAK,UAAU;AACf,SAAK,WAAW,MAAM;AAAA,EACxB;AAAA,EAEA,KAAK,SAAwC;AAC3C,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,UAAI,CAAC,KAAK,SAAS,OAAO;AACxB,cAAM,IAAI,MAAM,eAAe;AAAA,MACjC;AAEA,YAAM,OAAO,iBAAiB,OAAO;AACrC,UAAI,KAAK,QAAQ,MAAM,MAAM,IAAI,GAAG;AAClC,gBAAQ;AAAA,MACV,OAAO;AACL,aAAK,QAAQ,MAAM,KAAK,SAAS,OAAO;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAuB;AAC3B,QAAI,KAAK,SAAS;AAChB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,UAAU;AAAA,QACb,KAAK,aAAa;AAAA,QAClB,KAAK,aAAa,QAAQ,CAAC;AAAA,QAC3B;AAAA,UACE,KAAK,KAAK,aAAa;AAAA,UACvB,KAAK,KAAK,aAAa;AAAA,UACvB,OAAO,KAAK,aAAa,SAAS;AAAA,UAClC,QAAQ,KAAK,gBAAgB;AAAA,UAC7B,OAAO,CAAC,QAAQ,QAAQ,KAAK,aAAa,UAAU,SAAS;AAAA,QAC/D;AAAA,MACF;AAEA,WAAK,QAAQ,GAAG,SAAS,CAAC,UAAU;AAClC,YAAI,MAAM,SAAS,cAAc;AAE/B,eAAK,UAAU;AACf;AAAA,QACF;AAEA,eAAO,KAAK;AACZ,aAAK,UAAU,KAAK;AAAA,MACtB,CAAC;AAED,WAAK,QAAQ,GAAG,SAAS,MAAM;AAC7B,gBAAQ;AAAA,MACV,CAAC;AAGD,WAAK,QAAQ,GAAG,SAAS,CAAC,UAAU;AAClC,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,QACR,CAAC;AAED,aAAK,UAAU;AACf,aAAK,UAAU;AAAA,MACjB,CAAC;AAED,WAAK,QAAQ,OAAO,GAAG,SAAS,CAAC,UAAU;AACzC,aAAK,UAAU;AAAA,UACb;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAED,aAAK,UAAU,KAAK;AAAA,MACtB,CAAC;AAED,WAAK,QAAQ,QAAQ,GAAG,QAAQ,CAAC,UAAU;AACzC,aAAK,UAAU;AAAA,UACb,OAAO,MAAM,SAAS;AAAA,UACtB,MAAM;AAAA,QACR,CAAC;AAED,aAAK,WAAW,OAAO,KAAK;AAC5B,aAAK,kBAAkB;AAAA,MACzB,CAAC;AAED,WAAK,QAAQ,QAAQ,GAAG,SAAS,CAAC,UAAU;AAC1C,aAAK,UAAU;AAAA,UACb;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAED,aAAK,UAAU,KAAK;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEQ,oBAAoB;AAC1B,WAAO,MAAM;AACX,UAAI;AACF,cAAM,UAAU,KAAK,WAAW,YAAY;AAE5C,YAAI,YAAY,MAAM;AACpB;AAAA,QACF;AAEA,aAAK,UAAU;AAAA,UACb;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAED,aAAK,YAAY,OAAO;AAAA,MAC1B,SAAS,OAAO;AACd,aAAK,UAAU;AAAA,UACb;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAED,aAAK,UAAU,KAAc;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;;;ADxNA,KAAK,QAAQ,eAAe,QAAQ;AAEpC,IAAI,EAAE,iBAAiB,SAAS;AAE9B,SAAO,cAAc;AACvB;AAEA,IAAM,OAAO,MAAM,MAAM,QAAQ,QAAQ,IAAI,CAAC,EAC3C,WAAW,WAAW,EACtB,QAAQ,0BAA0B,kCAAkC,EACpE,WAAW,WAAW;AAAA,EACrB,cAAc;AAAA,EACd,UAAU;AAAA,EACV,MAAM;AACR,CAAC,EACA,WAAW,QAAQ;AAAA,EAClB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AACR,CAAC,EACA,IAAI,WAAW,EACf,QAAQ;AAAA,EACP,OAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,SAAS,CAAC,OAAO,QAAQ;AAAA,IACzB,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,gBAAgB;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AACF,CAAC,EACA,KAAK,EACL,WAAW;AAEd,IAAM,UAAU,OAAO,WAAmB;AACxC,QAAM,YAAY,IAAI,qBAAqB;AAAA,IACzC,MAAM,KAAK;AAAA,IACX,SAAS,KAAK;AAAA,IACd,KAAK,QAAQ;AAAA,IACb,SAAS,CAAC,UAAU;AAClB,UAAI,KAAK,OAAO;AACd,gBAAQ,MAAM,mBAAmB,KAAK;AAAA,MACxC;AAAA,IACF;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,OAAO,QAAQ,SAAS;AAChC;AAEA,IAAM,QAAQ,YAAY;AACxB,QAAM,SAAS,IAAI;AAAA,IACjB;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA;AAAA,MACE,cAAc,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,QAAM,QAAQ,MAAM;AAEpB,QAAM,gBAAgB,OAAO,iBAAiB;AAK9C,QAAM,qBAAqB,OAAO,sBAAsB;AAIxD,UAAQ,KAAK,8BAA8B,KAAK,IAAI;AAEpD,QAAM,eAAe,YAAY;AAC/B,UAAM,SAAS,IAAI,OAAO,eAAe;AAAA,MACvC,cAAc;AAAA,IAChB,CAAC;AAED,gBAAY;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,YAAY,IAAI,mBAAmB;AAAA,IACnC,MAAM,KAAK;AAAA,IACX,aAAa,KAAK,UAAU,KAAK,WAAW,QAAQ,OAAQ,KAAK,eAAe,KAAK;AAAA,IACrF,gBAAgB,KAAK,UAAU,KAAK,WAAW,WAAW,OAAQ,KAAK,kBAAkB,KAAK;AAAA,EAChG,CAAC;AACH;AAEA,IAAM,OAAO,YAAY;AACvB,UAAQ,GAAG,UAAU,MAAM;AACzB,YAAQ,KAAK,gCAAgC;AAE7C,eAAW,MAAM;AACf,cAAQ,KAAK,CAAC;AAAA,IAChB,GAAG,GAAI;AAAA,EACT,CAAC;AAED,MAAI;AACF,UAAM,MAAM;AAAA,EACd,SAAS,OAAO;AACd,YAAQ,MAAM,6BAA6B,KAAK;AAEhD,eAAW,MAAM;AACf,cAAQ,KAAK,CAAC;AAAA,IAChB,GAAG,GAAI;AAAA,EACT;AACF;AAEA,MAAM,KAAK;", "names": []}