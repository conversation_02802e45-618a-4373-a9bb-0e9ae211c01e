{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": "AAAA,kCAAkC;AAClC,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAkChE,8BAA8B;AAC9B,MAAM,OAAO,oBAAoB;IACxB,QAAQ,CAAiB;IACzB,QAAQ,CAAiB;IACzB,KAAK,CAAc;IAE1B;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IACtC,CAAC;IAEO,kBAAkB;QACxB,OAAO;YACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,eAAe,CAAC,YAAY;YACjF,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,aAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YACtG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,eAAe,CAAC,YAAY;YACjF,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,eAAe,CAAC,gBAAgB;YAC7F,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,eAAe,CAAC,gBAAgB;YAC7F,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YAC7H,cAAc,EAAE,aAAa,CAAC,eAAe;YAC7C,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,mBAAmB,CAAC,IAAI,aAAa,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YAC1H,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,gBAAgB,CAAC,IAAI,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YACjH,UAAU,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW;SAC7G,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,uBAAuB,CAAC,IAAI,eAAe,CAAC,2BAA2B,CAAC;QAC5H,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAEjF,OAAO;YACL,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YAC5H,iBAAiB,EAAE,YAAY;YAC/B,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,mBAAmB,CAAC,IAAI,aAAa,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YACzH,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YACpH,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,aAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YACtH,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,qBAAqB,CAAC,IAAI,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;SAChI,CAAC;IACJ,CAAC;IAEO,eAAe;QACrB,OAAO;YACL,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YAC1G,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,aAAa,CAAC,uBAAuB,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YAC3H,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YACvG,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;SACpF,CAAC;IACJ,CAAC;IAED,uEAAuE;IAChE,QAAQ;QACb,MAAM,SAAS,GAAG;YAChB,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC9B,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC9B,KAAK,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;SACzB,CAAC;QAEF,6BAA6B;QAC7B,SAAS,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEpC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,4BAA4B;IACrB,UAAU;QACf,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YACjC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC5C,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE;YAC1D,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE;YACvD,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE;YACrD,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE;SACzD,CAAC;IACJ,CAAC;CACF"}