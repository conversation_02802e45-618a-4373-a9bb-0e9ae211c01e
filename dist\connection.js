// MySQL Connection Management with Pooling
import { createPool } from 'mysql2/promise';
import { StringConstants, DefaultConfig } from './constants.js';
export class ConnectionPool {
    config;
    pool = null;
    healthCheckInterval = null;
    shutdownEvent = false;
    connectionStats = {
        pool_hits: 0,
        pool_waits: 0
    };
    constructor(config) {
        this.config = config;
    }
    // Initialize connection pool
    async initialize() {
        if (this.pool) {
            return;
        }
        try {
            const poolConfig = {
                host: this.config.host,
                port: this.config.port,
                user: this.config.user,
                password: this.config.password,
                database: this.config.database,
                connectionLimit: this.config.connectionLimit,
                acquireTimeout: this.config.connectTimeout * 1000,
                timeout: this.config.connectTimeout * 1000,
                charset: StringConstants.CHARSET,
                multipleStatements: false, // Security: disable multiple statements
                ssl: this.config.sslEnabled ? {} : false
            };
            this.pool = createPool(poolConfig);
            // Pre-create minimum connections
            await this.preCreateConnections();
            // Start health check
            this.startHealthCheck();
        }
        catch (error) {
            throw new Error(`${StringConstants.MSG_FAILED_TO_INIT_POOL} ${error}`);
        }
    }
    // Pre-create minimum connections
    async preCreateConnections() {
        if (!this.pool)
            return;
        try {
            const promises = [];
            for (let i = 0; i < this.config.minConnections; i++) {
                promises.push(this.pool.getConnection());
            }
            const connections = await Promise.all(promises);
            // Release connections back to pool
            connections.forEach(conn => conn.release());
        }
        catch (error) {
            // Pre-creation failure doesn't affect overall initialization
            console.warn('Warning: Failed to pre-create connections:', error);
        }
    }
    // Start health check
    startHealthCheck() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }
        this.healthCheckInterval = setInterval(() => {
            this.performHealthCheck();
        }, DefaultConfig.HEALTH_CHECK_INTERVAL * 1000);
    }
    // Perform health check
    async performHealthCheck() {
        if (!this.pool)
            return;
        try {
            const connection = await this.pool.getConnection();
            await connection.execute('SELECT 1');
            connection.release();
        }
        catch (error) {
            // Health check failure is not critical
            console.warn('Health check failed:', error);
        }
    }
    // Get database connection
    async getConnection() {
        if (!this.pool) {
            await this.initialize();
        }
        if (!this.pool) {
            throw new Error('Connection pool not initialized');
        }
        const startTime = Date.now();
        const connection = await this.pool.getConnection();
        const waitTime = Date.now() - startTime;
        // Update connection statistics
        if (waitTime > 100) { // More than 100ms wait
            this.connectionStats[StringConstants.FIELD_POOL_WAITS]++;
        }
        else {
            this.connectionStats[StringConstants.FIELD_POOL_HITS]++;
        }
        return connection;
    }
    // Get pool statistics
    getStats() {
        if (!this.pool) {
            return { [StringConstants.STATUS_KEY]: StringConstants.STATUS_NOT_INITIALIZED };
        }
        // Note: In a real implementation, we would access pool internals
        // For now, we'll return what we can safely access
        return {
            [StringConstants.FIELD_POOL_NAME]: StringConstants.POOL_NAME,
            [StringConstants.FIELD_POOL_SIZE]: this.config.connectionLimit,
            [StringConstants.FIELD_CONNECTION_STATS]: { ...this.connectionStats },
            [StringConstants.FIELD_HEALTH_CHECK_ACTIVE]: !!this.healthCheckInterval
        };
    }
    // Close connection pool
    async close() {
        this.shutdownEvent = true;
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }
        if (this.pool) {
            await this.pool.end();
            this.pool = null;
        }
    }
}
//# sourceMappingURL=connection.js.map