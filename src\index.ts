/**
 * MySQL MCP Server Implementation
 *
 * This file contains the complete MySQL Model Context Protocol (MCP) server implementation,
 * providing tools for database operations, schema management, and connection diagnostics.
 */

import { FastMCP } from 'fastmcp';
import { z } from 'zod';
import { MySQLManager } from './mysqlManager.js';
import { StringConstants } from './constants.js';

// Initialize global MySQL manager instance
const mysqlManager = new MySQLManager();

// Create FastMCP server instance
const mcp = new FastMCP({
  name: StringConstants.SERVER_NAME,
  version: "1.0.0"
});

/**
 * Tool: Execute MySQL Query
 * Executes arbitrary MySQL queries (SELECT, SHOW, DESCRIBE, etc.)
 */
mcp.addTool({
  name: 'mysql_query',
  description: 'Execute MySQL queries (SELECT, SHOW, DESCRIBE, etc.)',
  parameters: z.object({
    query: z.string().describe('SQL query to execute'),
    params: z.array(z.any()).optional().describe('Optional parameters for prepared statements')
  }),
  execute: async (args) => {
    try {
      if (!args.params) {
        args.params = [];
      }

      // Validate input parameters
      mysqlManager['validateInput'](args.query, "query");
      args.params.forEach((param, i) => {
        mysqlManager['validateInput'](param, `param_${i}`);
      });

      const result = await mysqlManager.executeQuery(args.query, args.params);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_QUERY_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Show Tables
 * Lists all tables in the current database
 */
mcp.addTool({
  name: 'mysql_show_tables',
  description: 'Show all tables in the current database',
  parameters: z.object({}),
  execute: async () => {
    try {
      const showTablesQuery = "SHOW TABLES";
      const result = await mysqlManager.executeQuery(showTablesQuery);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_SHOW_TABLES_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Describe Table
 * Describes the structure of a specified table
 */
mcp.addTool({
  name: 'mysql_describe_table',
  description: 'Describe the structure of a specified table',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to describe')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      const result = await mysqlManager['getTableSchemaCached'](args.table_name);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DESCRIBE_TABLE_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Select Data
 * Selects data from a table with optional conditions and limits
 */
mcp.addTool({
  name: 'mysql_select_data',
  description: 'Select data from a table with optional conditions and limits',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to select data from'),
    columns: z.array(z.string()).optional().describe('Optional list of column names to select'),
    where_clause: z.string().optional().describe('Optional WHERE clause for filtering'),
    limit: z.number().int().optional().describe('Optional limit on number of rows returned')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      if (!args.columns) {
        args.columns = ["*"];
      }

      args.columns.forEach(col => {
        if (col !== "*") {
          mysqlManager['validateInput'](col, "column");
        }
      });

      let query = `SELECT ${args.columns.join(', ')} FROM \`${args.table_name}\``;

      if (args.where_clause) {
        mysqlManager['validateInput'](args.where_clause, "where_clause");
        query += ` WHERE ${args.where_clause}`;
      }

      if (args.limit) {
        query += ` LIMIT ${Math.floor(args.limit)}`;
      }

      const result = await mysqlManager.executeQuery(query);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_SELECT_DATA_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Insert Data
 * Inserts new data into a table
 */
mcp.addTool({
  name: 'mysql_insert_data',
  description: 'Insert new data into a table',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to insert data into'),
    data: z.record(z.any()).describe('Key-value pairs of column names and values')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      Object.keys(args.data).forEach(key => {
        mysqlManager['validateInput'](key, "column_name");
        mysqlManager['validateInput'](args.data[key], "column_value");
      });

      const columns = Object.keys(args.data);
      const values = Object.values(args.data);
      const placeholders = columns.map(() => "?").join(", ");

      const query = `INSERT INTO \`${args.table_name}\` (\`${columns.join('`, `')}\`) VALUES (${placeholders})`;
      const result = await mysqlManager.executeQuery(query, values);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_INSERT_DATA_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Update Data
 * Updates existing data in a table based on specified conditions
 */
mcp.addTool({
  name: 'mysql_update_data',
  description: 'Update existing data in a table based on specified conditions',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to update'),
    data: z.record(z.any()).describe('Key-value pairs of column names and new values'),
    where_clause: z.string().describe('WHERE clause to specify which records to update (without WHERE keyword)')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      mysqlManager['validateInput'](args.where_clause, "where_clause");

      Object.keys(args.data).forEach(key => {
        mysqlManager['validateInput'](key, "column_name");
        mysqlManager['validateInput'](args.data[key], "column_value");
      });

      const columns = Object.keys(args.data);
      const values = Object.values(args.data);
      const setClause = columns.map(col => `\`${col}\` = ?`).join(", ");

      const query = `UPDATE \`${args.table_name}\` SET ${setClause} WHERE ${args.where_clause}`;
      const result = await mysqlManager.executeQuery(query, values);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_UPDATE_DATA_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Delete Data
 * Deletes data from a table based on specified conditions
 */
mcp.addTool({
  name: 'mysql_delete_data',
  description: 'Delete data from a table based on specified conditions',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to delete data from'),
    where_clause: z.string().describe('WHERE clause to specify which records to delete (without WHERE keyword)')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      mysqlManager['validateInput'](args.where_clause, "where_clause");

      const query = `DELETE FROM \`${args.table_name}\` WHERE ${args.where_clause}`;
      const result = await mysqlManager.executeQuery(query);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DELETE_DATA_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Get Database Schema
 * Retrieves database schema information including tables, columns, and constraints
 */
mcp.addTool({
  name: 'mysql_get_schema',
  description: 'Get database schema information including tables, columns, and constraints',
  parameters: z.object({
    table_name: z.string().optional().describe('Optional specific table name to get schema information for')
  }),
  execute: async (args) => {
    try {
      let query = `
        SELECT
          TABLE_NAME,
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
      `;

      const params: string[] = [];
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        query += " AND TABLE_NAME = ?";
        params.push(args.table_name);
      }

      query += " ORDER BY TABLE_NAME, ORDINAL_POSITION";

      const result = await mysqlManager.executeQuery(query, params.length > 0 ? params : undefined);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_SCHEMA_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Get Indexes
 * Retrieves index information for tables or all tables in the database
 */
mcp.addTool({
  name: 'mysql_get_indexes',
  description: 'Get index information for a specific table or all tables in the database',
  parameters: z.object({
    table_name: z.string().optional().describe('Optional specific table name to get index information for')
  }),
  execute: async (args) => {
    try {
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        // Use cached version for better performance
        const cacheKey = `indexes_${args.table_name}`;
        let cachedResult = mysqlManager['indexCache'].get(cacheKey);

        if (cachedResult === null) {
          const indexesQuery = `
            SELECT
              INDEX_NAME,
              COLUMN_NAME,
              NON_UNIQUE,
              SEQ_IN_INDEX,
              INDEX_TYPE
            FROM INFORMATION_SCHEMA.STATISTICS
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
            ORDER BY INDEX_NAME, SEQ_IN_INDEX
          `;
          const queryResult = await mysqlManager.executeQuery(indexesQuery, [args.table_name]);
          const result = JSON.stringify(queryResult, null, 2);
          mysqlManager['indexCache'].put(cacheKey, result);
          mysqlManager['metrics'].cacheMisses++;
          return result;
        } else {
          mysqlManager['metrics'].cacheHits++;
          return cachedResult;
        }
      } else {
        // Get index information for all tables (no caching)
        const query = `
          SELECT
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME,
            NON_UNIQUE,
            SEQ_IN_INDEX,
            INDEX_TYPE
          FROM INFORMATION_SCHEMA.STATISTICS
          WHERE TABLE_SCHEMA = DATABASE()
          ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
        `;
        const result = await mysqlManager.executeQuery(query);
        return JSON.stringify(result, null, 2);
      }
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_INDEXES_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Get Foreign Keys
 * Retrieves foreign key constraint information for tables or all tables in the database
 */
mcp.addTool({
  name: 'mysql_get_foreign_keys',
  description: 'Get foreign key constraint information for a specific table or all tables in the database',
  parameters: z.object({
    table_name: z.string().optional().describe('Optional specific table name to get foreign key information for')
  }),
  execute: async (args) => {
    try {
      let query = `
        SELECT
          TABLE_NAME,
          COLUMN_NAME,
          CONSTRAINT_NAME,
          REFERENCED_TABLE_NAME,
          REFERENCED_COLUMN_NAME,
          UPDATE_RULE,
          DELETE_RULE
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = DATABASE()
          AND REFERENCED_TABLE_NAME IS NOT NULL
      `;

      const params: string[] = [];
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        query += " AND TABLE_NAME = ?";
        params.push(args.table_name);
      }

      query += " ORDER BY TABLE_NAME, CONSTRAINT_NAME";

      const result = await mysqlManager.executeQuery(query, params.length > 0 ? params : undefined);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_FOREIGN_KEYS_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Create Table
 * Creates a new table with specified columns and constraints
 */
mcp.addTool({
  name: 'mysql_create_table',
  description: 'Create a new table with specified columns and constraints',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to create'),
    columns: z.array(
      z.object({
        name: z.string(),
        type: z.string(),
        nullable: z.boolean().optional(),
        default: z.string().optional(),
        primary_key: z.boolean().optional(),
        auto_increment: z.boolean().optional()
      })
    ).describe('Array of column definitions')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      const columnDefs: string[] = [];
      args.columns.forEach(col => {
        mysqlManager['validateInput'](col.name, 'column_name');
        mysqlManager['validateInput'](col.type, 'column_type');

        let definition = `\`${col.name}\` ${col.type}`;

        if (col.nullable === false) {
          definition += " NOT NULL";
        }
        if (col.auto_increment) {
          definition += " AUTO_INCREMENT";
        }
        if (col.default) {
          definition += ` DEFAULT ${col.default}`;
        }

        columnDefs.push(definition);
      });

      const primaryKeys = args.columns
        .filter(col => col.primary_key)
        .map(col => col.name);

      if (primaryKeys.length > 0) {
        columnDefs.push(`PRIMARY KEY (\`${primaryKeys.join('`, `')}\`)`);
      }

      const query = `CREATE TABLE \`${args.table_name}\` (${columnDefs.join(', ')})`;
      const result = await mysqlManager.executeQuery(query);

      // Invalidate caches after table creation
      mysqlManager.invalidateCaches("CREATE");

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_CREATE_TABLE_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Drop Table
 * Drops (deletes) a table from the database
 */
mcp.addTool({
  name: 'mysql_drop_table',
  description: 'Drop (delete) a table from the database',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to drop'),
    if_exists: z.boolean().optional().describe('Use IF EXISTS clause to avoid errors if table does not exist')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      const query = `DROP TABLE ${args.if_exists ? 'IF EXISTS' : ''} \`${args.table_name}\``;
      const result = await mysqlManager.executeQuery(query);

      // Invalidate caches after table drop
      mysqlManager.invalidateCaches("DROP");

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DROP_TABLE_FAILED} ${error.message}`);
    }
  }
});

/**
 * Tool: Diagnose Connection
 * Diagnoses MySQL connection status and configuration
 */
mcp.addTool({
  name: 'mysql_diagnose_connection',
  description: 'Diagnose MySQL connection status and configuration',
  parameters: z.object({}),
  execute: async () => {
    try {
      const diagnosis: Record<string, any> = {
        [StringConstants.FIELD_CONNECTION_POOL_STATUS]: mysqlManager['connectionPool'].getStats(),
        [StringConstants.FIELD_CONFIG]: mysqlManager['configManager'].toObject(),
        [StringConstants.FIELD_PERFORMANCE_METRICS]: mysqlManager.getPerformanceMetrics(),
        enhanced_metrics: mysqlManager['enhancedMetrics'].getComprehensiveMetrics()
      };

      // Execute a simple connection test
      try {
        const connectionTestQuery = "SELECT 1 as test_connection";
        const testResult = await mysqlManager.executeQuery(connectionTestQuery);
        diagnosis[StringConstants.FIELD_CONNECTION_TEST] = {
          [StringConstants.STATUS_KEY]: StringConstants.STATUS_SUCCESS,
          [StringConstants.FIELD_RESULT]: testResult
        };
      } catch (error: any) {
        diagnosis[StringConstants.FIELD_CONNECTION_TEST] = {
          [StringConstants.STATUS_KEY]: StringConstants.STATUS_FAILED,
          [StringConstants.ERROR_KEY]: error.message
        };
      }

      return JSON.stringify(diagnosis, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DIAGNOSE_FAILED} ${error.message}`);
    }
  }
});

/**
 * Signal Handlers for Graceful Shutdown
 * Ensures proper cleanup of database connections when the process is terminated
 */
process.on('SIGINT', async () => {
  console.error(`\n${StringConstants.MSG_SIGNAL_RECEIVED} SIGINT, ${StringConstants.MSG_GRACEFUL_SHUTDOWN}`);
  await mysqlManager.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.error(`\n${StringConstants.MSG_SIGNAL_RECEIVED} SIGTERM, ${StringConstants.MSG_GRACEFUL_SHUTDOWN}`);
  await mysqlManager.close();
  process.exit(0);
});

/**
 * Exports
 * Export the MCP server instance and MySQL manager for external use
 */
export { mcp, mysqlManager };

/**
 * Server Startup Function
 * Initializes and starts the MySQL MCP server
 */
export async function startServer() {
  try {
    console.error(StringConstants.MSG_SERVER_RUNNING);
    await mcp.start();
  } catch (error: any) {
    console.error(`${StringConstants.MSG_SERVER_ERROR} ${error.message}`);
    await mysqlManager.close();
    process.exit(1);
  }
}

/**
 * Auto-start Server
 * Automatically starts the server when this module is executed directly
 */
startServer();