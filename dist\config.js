// Configuration Management System
import { DefaultConfig, StringConstants } from './constants.js';
// Configuration manager class
export class ConfigurationManager {
    database;
    security;
    cache;
    constructor() {
        this.database = this.loadDatabaseConfig();
        this.security = this.loadSecurityConfig();
        this.cache = this.loadCacheConfig();
    }
    loadDatabaseConfig() {
        return {
            host: process.env[StringConstants.ENV_MYSQL_HOST] || StringConstants.DEFAULT_HOST,
            port: parseInt(process.env[StringConstants.ENV_MYSQL_PORT] || DefaultConfig.MYSQL_PORT.toString(), 10),
            user: process.env[StringConstants.ENV_MYSQL_USER] || StringConstants.DEFAULT_USER,
            password: process.env[StringConstants.ENV_MYSQL_PASSWORD] || StringConstants.DEFAULT_PASSWORD,
            database: process.env[StringConstants.ENV_MYSQL_DATABASE] || StringConstants.DEFAULT_DATABASE,
            connectionLimit: parseInt(process.env[StringConstants.ENV_CONNECTION_LIMIT] || DefaultConfig.CONNECTION_LIMIT.toString(), 10),
            minConnections: DefaultConfig.MIN_CONNECTIONS,
            connectTimeout: parseInt(process.env[StringConstants.ENV_CONNECT_TIMEOUT] || DefaultConfig.CONNECT_TIMEOUT.toString(), 10),
            idleTimeout: parseInt(process.env[StringConstants.ENV_IDLE_TIMEOUT] || DefaultConfig.IDLE_TIMEOUT.toString(), 10),
            sslEnabled: (process.env[StringConstants.ENV_MYSQL_SSL] || '').toLowerCase() === StringConstants.TRUE_STRING
        };
    }
    loadSecurityConfig() {
        const allowedTypesStr = process.env[StringConstants.ENV_ALLOWED_QUERY_TYPES] || StringConstants.DEFAULT_ALLOWED_QUERY_TYPES;
        const allowedTypes = allowedTypesStr.split(',').map(t => t.trim().toUpperCase());
        return {
            maxQueryLength: parseInt(process.env[StringConstants.ENV_MAX_QUERY_LENGTH] || DefaultConfig.MAX_QUERY_LENGTH.toString(), 10),
            allowedQueryTypes: allowedTypes,
            maxResultRows: parseInt(process.env[StringConstants.ENV_MAX_RESULT_ROWS] || DefaultConfig.MAX_RESULT_ROWS.toString(), 10),
            queryTimeout: parseInt(process.env[StringConstants.ENV_QUERY_TIMEOUT] || DefaultConfig.QUERY_TIMEOUT.toString(), 10),
            rateLimitMax: parseInt(process.env[StringConstants.ENV_RATE_LIMIT_MAX] || DefaultConfig.RATE_LIMIT_MAX.toString(), 10),
            rateLimitWindow: parseInt(process.env[StringConstants.ENV_RATE_LIMIT_WINDOW] || DefaultConfig.RATE_LIMIT_WINDOW.toString(), 10)
        };
    }
    loadCacheConfig() {
        return {
            schemaCacheSize: parseInt(process.env.SCHEMA_CACHE_SIZE || DefaultConfig.SCHEMA_CACHE_SIZE.toString(), 10),
            tableExistsCacheSize: parseInt(process.env.TABLE_EXISTS_CACHE_SIZE || DefaultConfig.TABLE_EXISTS_CACHE_SIZE.toString(), 10),
            indexCacheSize: parseInt(process.env.INDEX_CACHE_SIZE || DefaultConfig.INDEX_CACHE_SIZE.toString(), 10),
            cacheTTL: parseInt(process.env.CACHE_TTL || DefaultConfig.CACHE_TTL.toString(), 10)
        };
    }
    // Export configuration for diagnostics (masking sensitive information)
    toObject() {
        const configObj = {
            database: { ...this.database },
            security: { ...this.security },
            cache: { ...this.cache }
        };
        // Mask sensitive information
        configObj.database.password = '***';
        return configObj;
    }
    // Get configuration summary
    getSummary() {
        return {
            database_host: this.database.host,
            database_port: this.database.port.toString(),
            connection_limit: this.database.connectionLimit.toString(),
            max_result_rows: this.security.maxResultRows.toString(),
            rate_limit_max: this.security.rateLimitMax.toString(),
            schema_cache_size: this.cache.schemaCacheSize.toString()
        };
    }
}
//# sourceMappingURL=config.js.map